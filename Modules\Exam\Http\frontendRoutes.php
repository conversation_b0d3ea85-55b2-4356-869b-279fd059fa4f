<?php

use Illuminate\Routing\Router;

/** @var Router $router */

// use Modules\Exam\Mail\SendCertificate;

$router->group(['prefix' => '/exam'], function (Router $router) {
    $router->bind('exam', function ($id) {
        return app('Modules\Exam\Repositories\ExamRepository')->find($id);
    });
    $router->get('/', [
        'as' => 'exam.index',
        'uses' => 'PublicController@index',
    ]);
    $router->get('image/{filter_id}-{user_id}/{action}.png', [
        'as' => 'exam.image',
        'uses' => 'PublicController@image',
    ]);
    $router->group(['prefix' => '/{type}'], function (Router $router) {
        $router->get('/', [
            'as' => 'exam.list',
            'uses' => 'PublicController@list',
        ]);
        $router->get('{exam}/show', [
            'as' => 'exam.show',
            'uses' => 'PublicController@show',
        ]);
         $router->post('{access_token}/join', [
            'as' => 'exam.join',
            'uses' => 'PublicController@join',
        ]);
    });

    // Payment routes
    $router->group(['prefix' => '/payment', 'middleware' => 'auth'], function (Router $router) {
        $router->post('generate-qr', [
            'as' => 'exam.payment.generate-qr',
            'uses' => 'PaymentController@generateQR',
        ]);
        $router->post('check-status', [
            'as' => 'exam.payment.check-status',
            'uses' => 'PaymentController@checkStatus',
        ]);
        $router->post('confirm', [
            'as' => 'exam.payment.confirm',
            'uses' => 'PaymentController@confirmPayment',
        ]);
    });

    // Public payment routes
    $router->post('payment/callback', [
        'as' => 'exam.payment.callback',
        'uses' => 'PaymentController@callback',
    ]);
    $router->get('qr-image/{data}', [
        'as' => 'exam.qr-image',
        'uses' => 'PaymentController@qrImage',
    ]);

});

$router->group(['prefix' => '/user', 'middleware' => ['logged.in']], function (Router $router) {
    $router->bind('exam', function ($id) {
        return app('Modules\Exam\Repositories\ExamRepository')->find($id);
    });
    // $router->bind('schedule', function ($id) {
    //     return app('Modules\Exam\Repositories\ExamRepository')->findSchedule($id);
    // });
    
    // Fix: Replace array binding with a custom binding pattern
    $router->bind('examSchedule', function ($value) {
        list($examId, $scheduleNo) = explode('-', $value);
        return app('Modules\Exam\Repositories\ExamRepository')->findScheduleNo($examId, $scheduleNo);
    });
    
    // Then in your routes, use it like:
    // $router->get('exam/{examSchedule}', ...);
    $router->group(['prefix' => '/exam'], function (Router $router) {
        $router->get('/', [
            'as' => 'user.exam.index',
            'uses' => 'ApplicantController@index',
            // 'middleware' => 'can:exam.exams.index'
        ]);
        // $router->get('{schedule}/register', [
        //     'as' => 'user.exam.register',
        //     'uses' => 'ApplicantController@register',
        // ]); 
        $router->get('{access_token}/checkname', [
            'as' => 'user.exam.checkname',
            'uses' => 'ApplicantController@checkname',
        ]);
        $router->match(['get', 'post'], '{access_token}/checkname-confirm', [
            'as' => 'user.exam.checkname-confirm',
            'uses' => 'ApplicantController@checknameConfirm',
        ]);
        $router->get('{access_token}/download-certificate', [
            'as' => 'user.exam.download-certificate',
            'uses' => 'ApplicantController@downloadCertificate',
        ]);
        $router->get('{access_token}/send-certificate', [
            'as' => 'user.exam.send-certificate',
            'uses' => 'ApplicantController@sendCertificate',
        ]);
        $router->get('{access_token}/verify', [
            'as' => 'user.exam.verify',
            'uses' => 'ApplicantController@verify',
        ]);
        $router->group(['prefix' => '/{type}'], function (Router $router) {
            $router->get('{access_token}/unjoin', [
                'as' => 'user.exam.unjoin',
                'uses' => 'ApplicantController@unjoin',
            ]);
        });
    });
});

