<div class="box box-default">
    <div class="box-body">
        <div class="nav-tabs-custom nav-tabs-{{ $skin_bootstrap or 'primary' }}">
            <ul class="nav nav-tabs">
                <li class="active">
                    <a href="#tab_data" data-toggle="tab">{{ _trans('user::users.tabs.data') }}</a>
                </li>
                <li class=" ">
                    <a href="#tab_approve" data-toggle="tab">{{ _trans('user::users.tabs.approve data') }}</a>
                </li>
                <li class=" ">
                    <a href="#tab_role" data-toggle="tab">{{ _trans('user::users.tabs.roles') }}</a>
                </li>
                {{-- <li class=" ">
                    <a href="#tab_address" data-toggle="tab">{{ _trans('user::users.tabs.address book') }}</a>
                </li> --}}
                <li class=" ">
                    <a href="#tab_permission" data-toggle="tab">{{ _trans('user::users.tabs.permissions') }}</a>
                </li>
                <li class=" ">
                    <a href="#tab_password" data-toggle="tab">{{ _trans('user::users.tabs.new password') }}</a>
                </li>
                <li class=" ">
                    <a href="#tab_api_key" data-toggle="tab">{{ _trans('user::users.tabs.api key') }}</a>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="tab_data">
                    <div class="" style="padding-left: 100px">
                        <div class='form-group text-center' style="position: absolute; left: 20px;">
                            <label for="" style="margin-bottom: 20px;">รูปโปรไฟล์</label> <br>
                            @include('core::partials.custom-file-upload', [
                                'gallery' => 'avatar',
                                'name' => 'avatar',
                                'files' => $user->filesByZone('avatar')->get(),
                            ])
                            @yield('custom-upload-avatar')
                        </div>
                        @include('user::admin.partials.profile', ['model' => $item, 'user_career' => $item->profile['career']])
                    </div>
                    {{-- </div> --}}
                    {{-- <div class="col-md-4 col-sm-6 col-12 text-center">
                            <a class="clickable b btn_avatar" data-toggle="modal" data-target="#avatarModal">
                                <img src="{{ _get_avatar_user(old('avatar', $item->avatar), 'sm') }}"
                                    style="width: 128px" class="img-circle" alt="User Image" id="preview-avatar" /><br>
                                {!! Form::label('avatar', _trans('user::users.form.choose avatar')) !!}
                                {!! Form::hidden('avatar', old('avatar', $item->avatar), ['id' => 'input-avatar', 'class' => 'form-control']) !!}
                            </a>
                        </div> --}}
                    {{-- </div> --}}

                </div>
                <div class="tab-pane " id="tab_approve">
                    
                    @include('user::admin.partials.approve', ['model' => $item, 'user_career' => $item->profile['career']])
                    <div class="row">
                        <div class="col-sm-6">
                            <label for="verify_status">สถานะการตรวจสอบตัวตน</label>
                            <select name="verify_status" id="verify_status" class="form-control">
                            @foreach (config("asgard.user.data.verify_status", []) as $s => $vs)
                                <option value="{{ $vs['value'] }}" {{ $item->verify_status==$vs['value']?"selected":"" }}>{{ $vs['label'] }}</option>
                            @endforeach
                            </select>
                            {{-- <label class="zd-icheck zd-icheck-checkbox ">
                                <input type="hidden" name="verify_status" value="{{(int)!!$item->verified_at}}"><input 
                                    {{$item->verified_at?'checked':''}} type="checkbox"
                                    onclick="this.previousSibling.value=1-this.previousSibling.value">
                                <span class="checkmark"></span>
                                {{ _trans('user::users.form.is approved') }}
                            </label> --}}
                        </div>
                        @if($item->verified_at)
                        <div class="col-sm-6">
                            <label for="verify_status">&nbsp;</label>
                            <p for="">ตรวจสอบเมื่อ: {{ _date_thai("j F y H:i:s", $item->verified_at) }}</p>
                        </div>
                        @endif
                    </div>
                </div>
                <div class="tab-pane " id="tab_role">
                    <div class='form-group{{ $errors->has('roles[]') ? ' has-error' : '' }}'>
                        {!! Form::label('roles[]', _trans('user::users.form.roles')) !!}
                        {!! Form::select('roles[]', $roles->toArray(), old('roles[]', $item->roles), [
                            'class' => 'zd-select2 form-control',
                            'style' => 'width:100%',
                            'multiple' => 'multiple',
                        ]) !!}
                        {!! $errors->first('roles[]', '<span class="help-block">:message</span>') !!}
                    </div> 
                    <div class='form-group{{ $errors->has('expire_at') ? ' has-error' : '' }}'>
                        {!! Form::label('expire_at', _trans('user::users.form.expire at')) !!}
                        {!! Form::input('date', 'expire_at', old('expire_at', date("Y-m-d", strtotime($item->roles[0]->pivot->expire_at??("+".config("asgard.user.config.premuim_expire_time"))))), ['class' => 'form-control']) !!}
                        {!! $errors->first('expire_at', '<span class="help-block">:message</span>') !!}
                    </div>
                </div>
                <div class="tab-pane " id="tab_address">
                    {{-- @include('user::admin.partials.addressbook', ['model' => $item]) --}}
                </div>
                <div class="tab-pane " id="tab_permission">
                    @include('user::admin.partials.permissions', ['model' => $item])
                </div>
                <div class="tab-pane " id="tab_password">
                    <h4>{{ _trans('user::users.new password setup') }}</h4>

                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class='form-group{{ $errors->has('password') ? ' has-error' : '' }}'>
                                {!! Form::label('password', _trans('user::users.form.password')) !!}
                                {!! Form::input('password', 'password', old('password'), ['class' => 'form-control']) !!}
                                {!! $errors->first('password', '<span class="help-block">:message</span>') !!}
                            </div>
                            <div class='form-group{{ $errors->has('password_confirmation') ? ' has-error' : '' }}'>
                                {!! Form::label('password_confirmation', _trans('user::users.form.password-confirmation')) !!}
                                {!! Form::input('password', 'password_confirmation', old('password_confirmation'), ['class' => 'form-control']) !!}
                                {!! $errors->first('password_confirmation', '<span class="help-block">:message</span>') !!}
                            </div>
                        </div>
                        <div class="col-md-6 col-12">
                            <h4>{{ _trans('user::users.tabs.or send reset password mail') }}</h4>
                            <a class="btn btn-primary" href="javascript:void(0)">
                                {{ _trans('user::users.send reset password email') }}
                            </a>
                        </div>
                    </div>
                </div>


                <div class="tab-pane " id="tab_api_key">
                    <h4>{{ _trans('user::users.api-keys') }}</h4>

                    <div class="row">
                        <div class="col-md-6 col-12">
                            <ul class="list-unstyled">
                                @foreach (_array_wrap($tokens) as $token)
                                    <li style="margin-bottom: 20px;">
                                        <div class="input-group mb-4 ">
                                            {!! Form::text('access_token', old('access_token', $token->access_token), [
                                                'class' => 'form-control slug',
                                                'disabled' => 'disabled',
                                            ]) !!}

                                            <div class="input-group-addon border-0 p-0">
                                                <a class="btn btn-danger"
                                                    href="{{ route('admin.user.user.destroyApiKey', $token->id) }}"><i
                                                        class="fa fa-times"></i></a>
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        <div class="col-md-6 col-12">
                            <h4>{{ _trans('user::users.your api keys') }}</h4>
                            <a class="btn btn-primary" href="{{ _route('admin.user.user.createApiKey', $item->id) }}">
                                {{ _trans('user::users.generate new api key') }}
                            </a>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
    <div class="box-footer">
        @include('core::components.button-submit')
    </div>
</div>
