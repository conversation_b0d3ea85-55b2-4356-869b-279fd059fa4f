<?php

namespace Modules\Core\Services\Payment;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class QRPaymentService
{
    protected $config;
    protected $gateway;

    public function __construct($gateway = null)
    {
        $this->config = config('qr_payment');
        $this->gateway = $gateway ?: $this->config['default_gateway'];
    }

    /**
     * Generate QR code for payment
     *
     * @param array $paymentData
     * @return array
     */
    public function generateQRCode($paymentData)
    {
        try {
            $amount = $paymentData['amount'] ?? $this->config['exam']['default_fee'];
            $reference = $paymentData['reference'] ?? $this->generateReference();
            
            switch ($this->gateway) {
                case 'promptpay':
                    return $this->generatePromptPayQR($amount, $reference, $paymentData);
                case 'thai_qr':
                    return $this->generateThaiQR($amount, $reference, $paymentData);
                default:
                    throw new Exception("Unsupported payment gateway: {$this->gateway}");
            }
        } catch (Exception $e) {
            Log::error('QR Payment Generation Error: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Generate PromptPay QR Code
     *
     * @param float $amount
     * @param string $reference
     * @param array $paymentData
     * @return array
     */
    protected function generatePromptPayQR($amount, $reference, $paymentData)
    {
        $gatewayConfig = $this->config['gateways']['promptpay'];
        
        // Use phone number as default identifier
        $identifier = $gatewayConfig['phone_number'];
        
        // Generate PromptPay QR string
        $qrString = $this->buildPromptPayQRString($identifier, $amount, $reference);
        
        return [
            'qr_string' => $qrString,
            'qr_image_url' => $this->generateQRImageUrl($qrString),
            'amount' => $amount,
            'reference' => $reference,
            'gateway' => 'promptpay',
            'expires_at' => now()->addMinutes($this->config['timeout_minutes']),
            'payment_data' => [
                'identifier' => $identifier,
                'method' => 'promptpay',
            ]
        ];
    }

    /**
     * Generate Thai QR Payment
     *
     * @param float $amount
     * @param string $reference
     * @param array $paymentData
     * @return array
     */
    protected function generateThaiQR($amount, $reference, $paymentData)
    {
        $gatewayConfig = $this->config['gateways']['thai_qr'];
        
        // This would integrate with actual Thai QR API
        // For now, return a mock response
        return [
            'qr_string' => 'thai_qr_mock_string',
            'qr_image_url' => '/api/qr-image/' . base64_encode('thai_qr_mock_string'),
            'amount' => $amount,
            'reference' => $reference,
            'gateway' => 'thai_qr',
            'expires_at' => now()->addMinutes($this->config['timeout_minutes']),
            'payment_data' => [
                'merchant_id' => $gatewayConfig['merchant_id'],
                'method' => 'thai_qr',
            ]
        ];
    }

    /**
     * Build PromptPay QR String
     *
     * @param string $identifier
     * @param float $amount
     * @param string $reference
     * @return string
     */
    protected function buildPromptPayQRString($identifier, $amount, $reference)
    {
        // Simplified PromptPay QR generation
        // In production, use proper PromptPay QR library
        $payload = [
            '00' => '01', // Payload Format Indicator
            '01' => '12', // Point of Initiation Method
            '29' => [
                '00' => 'A000000677010111', // Application Identifier
                '01' => $this->formatPhoneNumber($identifier), // Phone number
            ],
            '53' => '764', // Transaction Currency (THB)
            '54' => sprintf('%.2f', $amount), // Transaction Amount
            '58' => 'TH', // Country Code
            '62' => [
                '07' => $reference, // Bill Number
            ]
        ];

        return $this->buildQRPayload($payload);
    }

    /**
     * Build QR Payload
     *
     * @param array $data
     * @return string
     */
    protected function buildQRPayload($data)
    {
        $payload = '';
        foreach ($data as $tag => $value) {
            if (is_array($value)) {
                $subPayload = $this->buildQRPayload($value);
                $payload .= $tag . sprintf('%02d', strlen($subPayload)) . $subPayload;
            } else {
                $payload .= $tag . sprintf('%02d', strlen($value)) . $value;
            }
        }
        
        // Add CRC (simplified)
        $payload .= '6304';
        $crc = $this->calculateCRC16($payload);
        $payload .= strtoupper(dechex($crc));
        
        return $payload;
    }

    /**
     * Format phone number for PromptPay
     *
     * @param string $phone
     * @return string
     */
    protected function formatPhoneNumber($phone)
    {
        // Remove non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Convert to international format
        if (substr($phone, 0, 1) === '0') {
            $phone = '66' . substr($phone, 1);
        }
        
        return $phone;
    }

    /**
     * Calculate CRC16
     *
     * @param string $data
     * @return int
     */
    protected function calculateCRC16($data)
    {
        // Simplified CRC16 calculation
        // In production, use proper CRC16 implementation
        return crc32($data) & 0xFFFF;
    }

    /**
     * Generate QR Image URL
     *
     * @param string $qrString
     * @return string
     */
    protected function generateQRImageUrl($qrString)
    {
        return '/api/qr-image/' . base64_encode($qrString);
    }

    /**
     * Generate payment reference
     *
     * @return string
     */
    protected function generateReference()
    {
        return 'PAY' . date('Ymd') . Str::random(6);
    }

    /**
     * Verify payment status
     *
     * @param string $reference
     * @return array
     */
    public function verifyPayment($reference)
    {
        // This would integrate with actual payment gateway APIs
        // For now, return a mock response
        return [
            'status' => 'pending', // pending, paid, failed, cancelled
            'reference' => $reference,
            'amount' => null,
            'paid_at' => null,
            'gateway_response' => null,
        ];
    }
}
