@extends('layouts.master-v2')

@section('title')
    หัวข้อสอบ {{ $exam->translate()->title }}
@stop
@include('school::partials.modal-select-school', ['disable_custom_school' => true])
@section('content')

    <section class="exam-section section-b-space">
        <div class="container-fluid">
            <div class="card mb-4" id="exam_{{ $exam->id }}" data-questions_count="{{ $exam->questions_count }}"
                data-join_message="{{ $exam->translate()->join_message }}"
                data-join_confirm="{{ $exam->translate()->join_confirm }}"
                data-join_linkto="{{ $exam->translate()->join_linkto }}">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden shine-effect ">
                    <div class="h-56 bg-red-500 chinese-pattern relative">
                        <div class="absolute inset-0 bg-gradient-to-r from-red-600 to-red-500 opacity-90 flex items-center justify-center">
                            <div class="text-center px-4 relative z-10">
                                <div class="chinese-symbol text-6xl font-bold text-yellow-300 mb-2 floating">汉语</div>
                                <p class="text-yellow-100 mt-2">ก้าวสู่ความสำเร็จในการเรียนรู้ภาษาจีน</p>
                                {{-- <h2 class="text-3xl md:text-4xl font-bold text-white">การสอบวัดระดับภาษาจีน HSK</h2> --}}
                                <h2 class="text-3xl md:text-4xl font-bold text-white">{!! nl2br($exam->translate()->title) !!}</h2>
                                <p class=" text-white mt-2">{!! nl2br($exam->translate()->summary) !!}</p>
                            </div>
                        </div>
                        <!-- Decorative elements -->
                        <div class="absolute top-4 left-4 text-white opacity-20 text-6xl chinese-symbol">学</div>
                        <div class="absolute bottom-4 right-4 text-white opacity-20 text-6xl chinese-symbol">习</div>
                    </div>
                    {{-- <div class="card-body"> --}} 
                    {{-- <h2>{!! nl2br($exam->translate()->title) !!}</h2>
                    <small>{!! nl2br($exam->translate()->summary) !!}</small> --}}
                </div>
            </div>
            <div class="row justify-content-center">

                <div class="col-lg-6 col-12">
                    @if ($v = $exam->files[0])
                        <div class="card mb-4">
                            <div class="card-body p-0">
                                <div id="demo-test-gallery" class="demo-gallery row">

                                    <a href="{{ $v->path }}" class="col-12"
                                        data-size="{{ $v->fileinfo['width'] }}x{{ $v->fileinfo['height'] }}"
                                        data-med="{{ $v->path }}"
                                        data-med-size="{{ $v->fileinfo['width'] * 1.5 }}x{{ $v->fileinfo['height'] * 1.5 }}"
                                        data-author="">
                                        <img src="{{ $v->path }}" alt="" width="100%" class="rounded-lg" />
                                    </a>

                                </div>
                            </div>
                        </div>
                    @endif

                    @if($exam->translate()->page_body != "")
                        <div class="card mb-4">
                            <div id="page_body" class="card-body">
                                {!! $exam->translate()->page_body !!}
                            </div>
                        </div>
                    @endif


{{-- ============== --}}
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden  mb-4">
                        {{-- <div class="h-56 bg-red-500 chinese-pattern relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-red-600 to-red-500 opacity-90 flex items-center justify-center">
                                <div class="text-center px-4 relative z-10">
                                    <div class="chinese-symbol text-6xl font-bold text-yellow-300 mb-2 floating">汉语</div>
                                    <h2 class="text-3xl md:text-4xl font-bold text-white">การสอบวัดระดับภาษาจีน HSK</h2>
                                    <p class="text-yellow-100 mt-2">ก้าวสู่ความสำเร็จในการเรียนรู้ภาษาจีน</p>
                                </div>
                            </div>
                            <!-- Decorative elements -->
                            <div class="absolute top-4 left-4 text-white opacity-20 text-6xl chinese-symbol">学</div>
                            <div class="absolute bottom-4 right-4 text-white opacity-20 text-6xl chinese-symbol">习</div>
                        </div> --}}
                        <div class="p-3">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-2xl font-semibold text-gray-800">ทำไมต้องสอบ HSK?</h3>
                                {{-- <div class="w-10 h-10 bg-red-50 icon-rounded rounded-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div> --}}
                            </div>
                            
                            <div class="grid grid-cols-1 md-grid-cols-2 gap-4 mb-4">
                                <div class="bg-gradient-to-br from-red-50 to-white p-4 rounded-lg border border-red-100 shadow-sm transform transition-transform hover-scale-105">
                                    <div class="flex items-start">
                                        <div class="bg-red-500 icon-rounded rounded-full p-2 mr-3 text-white">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">มาตรฐานสากล</h4>
                                            <p class="text-sm text-gray-600">เป็นการสอบที่ได้รับการยอมรับทั่วโลก</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-gradient-to-br from-red-50 to-white p-4 rounded-lg border border-red-100 shadow-sm transform transition-transform hover-scale-105">
                                    <div class="flex items-start">
                                        <div class="bg-red-500 icon-rounded rounded-full p-2 mr-3 text-white">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">โอกาสการศึกษา</h4>
                                            <p class="text-sm text-gray-600">เพิ่มโอกาสในการศึกษาต่อที่ประเทศจีน</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-gradient-to-br from-red-50 to-white p-4 rounded-lg border border-red-100 shadow-sm transform transition-transform hover-scale-105">
                                    <div class="flex items-start">
                                        <div class="bg-red-500 icon-rounded rounded-full p-2 mr-3 text-white">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">โอกาสการทำงาน</h4>
                                            <p class="text-sm text-gray-600">เพิ่มโอกาสในการทำงานกับบริษัทจีน</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-gradient-to-br from-red-50 to-white p-4 rounded-lg border border-red-100 shadow-sm transform transition-transform hover-scale-105">
                                    <div class="flex items-start">
                                        <div class="bg-red-500 icon-rounded rounded-full p-2 mr-3 text-white">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">ประเมินความก้าวหน้า</h4>
                                            <p class="text-sm text-gray-600">วัดระดับความสามารถทางภาษาจีนของตนเอง</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-red-50 border border-red-100 rounded-lg p-5 mb-4">
                                <h4 class="font-semibold text-red-800 mb-3 flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                                    </svg>
                                    ระดับการสอบ HSK
                                </h4>
                                <div class="grid grid-cols-1 md-grid-cols-3 gap-2 text-center">
                                    <div class="bg-white p-3 rounded-lg border border-red-200 shadow-sm transform transition-transform hover-scale-105">
                                        <div class="font-bold text-red-800 text-lg">HSK 1-2</div>
                                        <div class="text-sm text-gray-600">ระดับพื้นฐาน</div>
                                        <div class="text-xs text-gray-500 mt-1">150-300 คำศัพท์</div>
                                    </div>
                                    <div class="bg-white p-3 rounded-lg border border-red-200 shadow-sm transform transition-transform hover-scale-105">
                                        <div class="font-bold text-red-800 text-lg">HSK 3-4</div>
                                        <div class="text-sm text-gray-600">ระดับกลาง</div>
                                        <div class="text-xs text-gray-500 mt-1">600-1,200 คำศัพท์</div>
                                    </div>
                                    <div class="bg-white p-3 rounded-lg border border-red-200 shadow-sm transform transition-transform hover-scale-105">
                                        <div class="font-bold text-red-800 text-lg">HSK 5-6</div>
                                        <div class="text-sm text-gray-600">ระดับสูง</div>
                                        <div class="text-xs text-gray-500 mt-1">2,500-5,000 คำศัพท์</div>
                                    </div>
                                </div>
                            </div>

                            {{-- <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-5">
                                <h4 class="font-semibold text-yellow-800 mb-3 flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                    กำหนดการสอบครั้งถัดไป
                                </h4>
                                <div class="grid grid-cols-1 md-grid-cols-2 gap-4">
                                    <div class="bg-white p-3 rounded-lg border border-yellow-200 shadow-sm">
                                        <div class="font-semibold text-yellow-800">15 ธันวาคม 2566</div>
                                        <div class="text-sm text-gray-600">ปิดรับสมัคร: 30 พฤศจิกายน 2566</div>
                                        <div class="mt-1 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded inline-block">เปิดรับสมัครแล้ว</div>
                                    </div>
                                    <div class="bg-white p-3 rounded-lg border border-yellow-200 shadow-sm">
                                        <div class="font-semibold text-yellow-800">25 กุมภาพันธ์ 2567</div>
                                        <div class="text-sm text-gray-600">ปิดรับสมัคร: 10 กุมภาพันธ์ 2567</div>
                                        <div class="mt-1 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded inline-block">เร็วๆ นี้</div>
                                    </div>
                                </div>
                            </div> --}}
                        </div>
                    </div>
{{-- ============== --}}


                </div>
                <div class="col-lg-6 col-12">
                    <div class="card">
                        <div class="card-body">
                            <h4>ตารางสอบ</h4>
                            <table class="table mt-3">
                                @foreach ($exam->schedules->sortBy("no") as $s => $schedule)
                                    <tr>
                                        <td id="schedule_title_{{ $schedule->id }}">
                                            @if ($schedule->no != '')
                                                {{-- ศูนย์สอบที่ --}}
                                                <h4 class="custom-font-pink-light mb-0">
                                                    {{ !empty($schedule->no_alias) ? $schedule->no_alias : 'ศูนย์สอบที่ ' . $schedule->no }}
                                                </h4>
                                            @endif
                                        </td>
                                        <td id="schedule_time_{{ $schedule->id }}">
                                            @if ($schedule->no != '')
                                                {{ _date_thai('D j M y', $schedule->date) }}

                                                @if (!$schedule->status)
                                                    <h5 class="text-danger text-nowrap mb-0 ml-2 d-inline-block">
                                                        << ปิดการรับสมัครแล้ว</h5>
                                                @endif 
                                            @endif
                                        </td> 
                                    </tr>
                                    <tr>
                                        <td colspan="3" style="border-top: none" class="pt-0">
                                            {{ $schedule->location }}</td>
                                    </tr>
                                @endforeach
                            </table>
                        </div>
                    </div>
                    <div class="card">
                        {{-- @auth --}}
                            <div class="card-body">
                                <h4>แบบฟอร์มสมัครสอบ</h4>
                                {!! Form::open([
                                    'route' => ['exam.join', [$type, $exam->access_token]],
                                    'method' => 'post',
                                    'enctype' => 'multipart/form-data',
                                    'id' => 'form-exam',
                                ]) !!}

                                <div class="row p-3">
                                    <div class="col-12">
                                        <label for="schedule_id">เลือกศูนย์สอบที่ต้องการเข้าสอบ *</label>
                                        <select name="schedule_id" id="schedule_id" class="form-control" required>
                                            <option value="">- ต้องระบุ -</option>
                                            @foreach ($exam->schedules->sortBy("no") as $s => $schedule)
                                                @if ($schedule->status)
                                                    <option value="{{ $schedule->id }}">
                                                        {{ !empty($schedule->no_alias) ? $schedule->no_alias : 'ศูนย์สอบที่ ' . $schedule->no }} {{ $schedule->location }}
                                                    </option>
                                                @endif
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                 <div class="row p-3 ">
                                    <label class="col-12 block text-sm font-medium text-gray-700 mb-2">ระดับการสอบ</label>
                                    <div class="col-12 grid grid-cols-3 gap-3">
                                        <label class="font-bold text-red-800 flex items-center mr-1 p-3 border border-gray-200 rounded-lg cursor-pointer hover-bg-red-50 hover-border-red-200 transition-colors bg-red-100 border-red-300">
                                            <input type="radio" name="exam_level" value="1" class="text-red-800 focus:ring-red-500 h-4 w-4" required="">
                                            <span class="ml-2 text-sm">HSK 1</span>
                                        </label>
                                        <label class="font-bold text-red-800 flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover-bg-red-50 hover-border-red-200 transition-colors">
                                            <input type="radio" name="exam_level" value="2" class="text-red-800 focus:ring-red-500 h-4 w-4">
                                            <span class="ml-2 text-sm">HSK 2</span>
                                        </label>
                                        <label class="font-bold text-red-800 flex items-center ml-1 p-3 border border-gray-200 rounded-lg cursor-pointer hover-bg-red-50 hover-border-red-200 transition-colors">
                                            <input type="radio" name="exam_level" value="3" class="text-red-800 focus:ring-red-500 h-4 w-4">
                                            <span class="ml-2 text-sm">HSK 3</span>
                                        </label>
                                        <label class="font-bold text-red-800 flex items-center mr-1 p-3 border border-gray-200 rounded-lg cursor-pointer hover-bg-red-50 hover-border-red-200 transition-colors">
                                            <input type="radio" name="exam_level" value="4" class="text-red-800 focus:ring-red-500 h-4 w-4">
                                            <span class="ml-2 text-sm">HSK 4</span>
                                        </label>
                                        <label class="font-bold text-red-800 flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover-bg-red-50 hover-border-red-200 transition-colors">
                                            <input type="radio" name="exam_level" value="5" class="text-red-800 focus:ring-red-500 h-4 w-4">
                                            <span class="ml-2 text-sm">HSK 5</span>
                                        </label>
                                        <label class="font-bold text-red-800 flex items-center ml-1 p-3 border border-gray-200 rounded-lg cursor-pointer hover-bg-red-50 hover-border-red-200 transition-colors">
                                            <input type="radio" name="exam_level" value="6" class="text-red-800 focus:ring-red-500 h-4 w-4">
                                            <span class="ml-2 text-sm">HSK 6</span>
                                        </label>
                                    </div>
                                </div>
                                <div id="box_schedule_detail" class="row px-3 border-bottom">
                                    @foreach ($exam->schedules as $s => $schedule)
                                        <div id="schedule_detail_{{ $schedule->id }}" class="schedule_detail pb-3 d-none">
                                            {!! nl2br($schedule->detail) !!}
                                        </div>
                                    @endforeach
                                </div>
                                <div class="row p-3 border-bottom">
                                    <h4 class="mb-3">ข้อมูลผู้เข้าสอบ</h4>
                                    <div class="col-12">
                                        <div class="row">
                                            <div class="form-group col-4">
                                                <label for="pre_name_th">คำนำหน้าชื่อ (TH) *</label>
                                                <select name="pre_name_th" id="pre_name_th" class="form-control" required>
                                                    @foreach (config('asgard.exam.data.pre_name_th') as $p)
                                                        <option {{ auth()->user()->pre_name == $p ? 'selected' : '' }}
                                                            value="{{ $p }}">{{ $p }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="form-group col-4">
                                                <label for="first_name_th">ชื่อ (TH) *</label>
                                                <input type="text" name="first_name_th" id="first_name_th"
                                                    class="form-control" required value="{{ auth()->user()->first_name }}">
                                            </div>
                                            <div class="form-group col-4">
                                                <label for="last_name_th">นามสกุล (TH) *</label>
                                                <input type="text" name="last_name_th" id="last_name_th"
                                                    class="form-control" required value="{{ auth()->user()->last_name }}">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-4">
                                                <label for="pre_name_en">Pre Name (EN) *</label>
                                                <select name="pre_name_en" id="pre_name_en" class="form-control" required>
                                                    @foreach (config('asgard.exam.data.pre_name_en') as $p)
                                                        <option 
                                                            value="{{ $p }}">{{ $p }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="form-group col-4">
                                                <label for="first_name_en">First Name (EN) *</label>
                                                <input type="text" name="first_name_en" id="first_name_en"
                                                    class="form-control" required value="">
                                            </div>
                                            <div class="form-group col-4">
                                                <label for="last_name_en">Last Name (EN) *</label>
                                                <input type="text" name="last_name_en" id="last_name_en"
                                                    class="form-control" required value="">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-6">
                                                <label for="contact_phone">เบอร์โทร / Phone *</label>
                                                <input type="text" name="contact_phone" id="contact_phone" class="form-control"
                                                    required value="{{ auth()->user()->phone }}">
                                            </div>
                                            <div class="form-group col-6">
                                                <label for="contact_email">อีเมล์ / Email *</label>
                                                <input type="text" name="contact_email" id="contact_email"
                                                    class="form-control validate-email" required
                                                    value="{{ auth()->user()->email }}">
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-12">
                                                <label for="id_card">เลขประจําตัวประชาชน / ID Card *</label>
                                                <input type="text" name="id_card" id="id_card" class="form-control" pattern="[0-9]{13}"
                                                    required value="{{ auth()->user()->profile->id_card }}">
                                            </div> 
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-6">
                                                <label for="position">ตําแหน่ง / Position *</label>
                                                <input type="text" name="position" id="position" class="form-control"
                                                    required value="">
                                            </div>
                                            <div class="form-group col-6">
                                                <label for="academic">วิทยฐานะ / Academic qualification *</label>
                                                <input type="text" name="academic" id="academic" class="form-control" 
                                                    required value="">
                                            </div>
                                        </div>
                                        <div class="row"> 
                                            <div class="form-group col-12">
                                                {!! Form::hidden('school_id', old('school_id'), ['id' => 'o_school_id']) !!}
                                                {!! Form::hidden('school_no', old('school_no'), ['id' => 'o_school_no']) !!} 
                                                {!! Form::hidden('school_info', old('school_info'), ['id' => 'o_school_info']) !!}
                                                <div
                                                    class="form-group has-feedback {{ $errors->has('school_name') ? ' has-error has-feedback' : '' }}">
                                                    {!! Form::label('school_name', 'โรงเรียน / School') !!}
                                                    <div class="input-group">
                                                        <div class="input-group-prepend" data-toggle="modal"
                                                            data-target="#modal-school-selection">
                                                            <button type="button" class="btn bg-pink">
                                                                <i class="fa fa-search"></i>
                                                            </button>
                                                        </div>
                                                        <!-- /btn-group -->
                                                        {!! Form::text('school_name', old('school_name'), [
                                                            'required' => 'required',
                                                            'readonly' => '',
                                                            'data-toggle' => 'modal',
                                                            'data-target' => '#modal-school-selection',
                                                            'class' => 'form-control',
                                                            'placeholder' => _trans('user::users.form.school_name') . ' *',
                                                            'id' => 'o_school_name',
                                                        ]) !!}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-12">
                                                <label for="school_area">สังกัดสํานักงานเขตพื้นที่การศึกษา<br>Affiliation of the Educational Service Area Office *</label>
                                                <select name="school_area" id="school_area" class="form-control select2" required>
                                                    <option value="">- ต้องระบุ -</option>
                                                    @foreach ($school_area as $a)
                                                        <option value="{{ $a->type }}: {{ $a->name }}">{{ $a->type }}: {{ $a->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div> 
                                        </div>
                                    </div>
                                </div> 
                               
                                {{-- <div class="row p-3 ">
                                    <h4 class=" mb-3">ข้อมูลจัดส่งและการออกใบเสร็จ</h4>
                                    <div class="col-12 p-2 mb-3" style="border: 2px solid #ff8084;">
                                        <table class="col-12 ">
                                            <tr>
                                                <td colspan="2"><b class="custom-font-pink-light mb-0">ช่องทางการโอนเงิน</b></td> 
                                            </tr>
                                            <tr>
                                                <td style="width: 200px"><b>ธนาคาร : กรุงไทย</b></td>
                                                <td><b>ชื่อบัญชี : องค์การค้าของ สกสค.(น้ํามัน)</b></td>
                                            </tr>
                                            <tr>
                                                <td><b>เลขที่ : 011-1-17355-8</b> </td>
                                                <td><b>จำนวนเงิน : 400 บาท (สี่ร้อยบาทถ้วน)</b></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-12 p-0">
                                        <div class="row">  
                                            <div class="form-group col-10">
                                                <label class="mb-0" for="addr_billing">ชื่อ-ที่อยู่ สำหรับออกใบกำกับภาษีและจัดส่ง *</label><br>
                                                <small>กรณีไม่ต้องการรับกำกับภาษีสำหรับเบิกต้นสังกัดกรุณาใส่เครื่องหมาย " - "</small><br>
                                                <b class="text-danger">* ไม่สามารถขอย้อนหลังได้ กรุณากรอกให้ถูกต้อง</b>
                                                <textarea id="addr_billing" name="addr_billing" class="form-control" rows="2" required></textarea>
                                                <label for="tax_id" class="mb-0 mt-1">เลขที่ใบกำกับภาษี *</label>
                                                <input id="tax_id" name="tax_id" class="form-control" required
                                                    placeholder="">
                                            </div>
                                            <div class='form-group col-2 box-file'>
                                                <label for="slip" class="text-nowrap">สลิปโอนเงิน *</label>
                                                @include('core::partials.custom-file-upload', [
                                                    'required' => 'required',
                                                    'name' => 'slip',
                                                    'label' => '.png /.jpg',
                                                    'icon' => 'upload',
                                                    'files' => [],
                                                ])
                                                @yield('custom-upload-slip')
                                            </div>
                                        </div>
                                    </div> 
                                </div> --}}
                                <button id="btn-submit"
                                    class="form-control btn btn-lg bg-red-500 text-white">ส่งข้อมูล</button>
                                    {{-- class="form-control btn btn-lg custom-bg-pink-light">ส่งข้อมูล</button> --}}
                                {!! Form::close() !!}
                            </div>
                        {{-- @else
                            <div class="card-header text-center">
                                <h4>แบบฟอร์มสมัครสอบ</h4>
                            </div>
                            <div class="card-body text-center">
                                <p>กรุณาเข้าสู่ระบบก่อนกรอกรายละเอียด</p>
                                <p><a target="_blank" href="{{ route('login') }}?redirect_url={{ request()->fullUrl() }}"
                                        class="btn btn-lg btn-warning d-inline-block"><i class="fa fa-pencil"></i>
                                        คลิกเพื่อเข้าสู่ระบบ</a></p>
                            </div>
                        @endauth --}}
                    </div>


                    {{-- <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h4>เพิ่มเติม:</h4>
                                    <ul>
                                        <li class="d-block">- หากต้องการสั่งซื้อ Boardgame เพิ่มเติม โปรดติดต่อ Line@ : @ssplearningplanet </li> 
                                    </ul>
                                </div>

                                <div class="col-md-4 col-6">
                                    <a target="_blank"
                                        href="{{ config('asgard.exam.data.types.' . $type . '.qr.line.link') }}">
                                        <img src="{{ _asset('assets/images/' . config('asgard.exam.data.types.' . $type . '.qr.line.img')) }}"
                                            alt="" width="100%">
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div> --}}

                </div>

            </div>
        </div>
    </section>

@stop
@include('exam::public.partials.exam-script')
@include('core::partials.photoswipe')
@push('js-stack')
    <div class="d-none" id="template_polls_{{ $exam->id }}" data-use_poll_type="{{ $exam->use_poll_type }}"
        data-ext_poll_id="{{ $exam->ext_poll_id }}">
        @include('exam::public.partials.template-polls')
    </div> 
    <script src="{{ _asset('modules/core/vendor/select2/4.1.0-rc.0/select2.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            $('.select2').select2(); 
        });
    </script>
@endpush

@push('css-stack')
    <link rel="stylesheet" type="text/css" href="{{ _asset('modules/core/vendor/select2/4.1.0-rc.0/select2.min.css') }}">

    <style type="text/css">
        .select2-results__options{
            grid-template-columns: repeat(2, minmax(0, 1fr));
            display: grid
        }

        .label {
            padding: 5px 5px 3px;
            border-radius: 4px;
            font-weight: 600;
            white-space: nowrap;
        }

        #page_body ol,
        #page_body ul {
            padding-left: 18px;
            /* display: block; */
        }

        /* #page_body li { */
            /* display: list-item; */
            /* display: block; */
        /* } */


        .has-error .zd-btn-upload,
        .has-error .custom-file-upload,
        .has-error .input-group-prepend,
        .has-error .select2-container,
        .has-error label {
            color: red
        }

        .has-error .zd-btn-upload,
        .has-error .custom-file-upload label,
        .has-error .input-group-prepend,
        .has-error .select2-container,
        .has-error select,
        .has-error textarea,
        .has-error input {
            border: 1px solid red
        }
 
 

        /*=========================*/
        /*=========================*/
        .form-input:focus {
            border-color: #e53e3e;
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.2);
        }
        .bg-gradient-red {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        }
        .chinese-pattern {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 14rem;
            position: relative;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e53e3e' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .chinese-pattern > .inset-0{
            opacity: 0.9; 
            /* --tw-gradient-to: #ef4444 var(--tw-gradient-to-position); 
            --tw-gradient-from: #dc2626 var(--tw-gradient-from-position); */
            --tw-gradient-to: #851B1D var(--tw-gradient-to-position); 
            --tw-gradient-from: #851B1D var(--tw-gradient-from-position);
            /* --tw-gradient-to: #5D1519 var(--tw-gradient-to-position); 
            --tw-gradient-from: #5D1519 var(--tw-gradient-from-position); */
            --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position);
            --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); 
            background-image: linear-gradient(to right, var(--tw-gradient-stops)); 
            /* display: flex;
            justify-content: center; 
            align-items: center;  */
            inset: 0px;
        } 
        .chinese-pattern > .inset-0 p {
            --tw-text-opacity: 1;
            color: rgb(254 249 195 / var(--tw-text-opacity, 1));
        }
        .chinese-pattern > .inset-0 .chinese-symbol{
            font-family: 'SimSun', 'STSong', serif;
            --tw-text-opacity: 1;
            color: rgb(253 224 71 / var(--tw-text-opacity, 1));
            font-weight: 700;
            font-size: 3.75rem;
            line-height: 1;
        } 
        .chinese-pattern > .chinese-symbol {
            font-family: 'SimSun', 'STSong', serif; 
            opacity: 0.2;
            --tw-text-opacity: 1;
            color: rgb(255 255 255 / var(--tw-text-opacity, 1));
            font-size: 3.75rem;
            line-height: 1;
            position: absolute;
        } 
        .chinese-pattern > .chinese-symbol.top-4 {
            top: 1rem;
            left: 1rem;
        } 
        .chinese-pattern > .chinese-symbol.bottom-4 {
            right: 1rem;
            bottom: 1rem;
        } 
 
        .shine-effect {
            position: relative;
            overflow: hidden;
        }

        .shine-effect::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
            transform: rotate(30deg);
            animation: shine 3s infinite;
        }
        @keyframes shine {
            0% {transform: translateX(-100%) rotate(30deg);}
            100% {transform: translateX(100%) rotate(30deg);}
        }
        .floating {animation: floating 3s ease-in-out infinite;}
        @keyframes floating {
            0% {transform: translateY(0px);}
            50% {transform: translateY(-10px);}
            100% {transform: translateY(0px);}
        }


        .transition-transform {
            transition-property: transform;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }
        .shadow-sm {
            --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
            box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
        }
        
        .font-bold {font-weight: 700;}
        .font-semibold {font-weight: 600;}
        .p-4 {padding: 1rem;}
        .transform {transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));}
        .flex {display: flex;} 
        .items-start {align-items: flex-start;}
        .rounded-full {border-radius: 9999px;}
        .rounded-lg {border-radius: 0.5rem;}
        .icon-rounded{width: 36px;height: 36px;}
        .gap-2 {gap: 0.5rem;}
        .gap-4 {gap: 1rem;}
        .w-5 {width: 1.25rem;}
        .h-5 {height: 1.25rem;}
        .grid {display: grid;}
        .grid-cols-1 {grid-template-columns: repeat(1, minmax(0, 1fr));}
        .grid-cols-3 {grid-template-columns: repeat(3, minmax(0, 1fr));}
        @media (min-width: 768px) {
            .md-grid-cols-2 {grid-template-columns: repeat(2, minmax(0, 1fr));}
            .md-grid-cols-3 {grid-template-columns: repeat(3, minmax(0, 1fr));}
        }
        .to-white {
            --tw-gradient-to: #fff var(--tw-gradient-to-position);
        }
        .from-red-50 {
            --tw-gradient-from: #fef2f2 var(--tw-gradient-from-position);
            --tw-gradient-to: rgb(254 242 242 / 0) var(--tw-gradient-to-position);
            --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
        } 
        .bg-red-50 {--tw-bg-opacity: 1;background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));}
        .bg-red-500 {
            --tw-bg-opacity: 1;
            /* background-color: rgb(93 21 25 / var(--tw-bg-opacity, 1)); */
            /* background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1)); */
            background-color: rgb(133 27 29 / var(--tw-bg-opacity, 1));
        } 
        .bg-gradient-to-br {background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));}
        .border-red-100 {--tw-border-opacity: 1;border-color: rgb(254 226 226 / var(--tw-border-opacity, 1));}
        .hover-scale-105:hover{--tw-scale-x:1.05;--tw-scale-y:1.05;transform:translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}
        .hover-border-red-200:hover{--tw-border-opacity:1;border-color:rgb(254 202 202 / var(--tw-border-opacity, 1))}
        .hover-bg-red-50:hover{--tw-bg-opacity:1;background-color:rgb(254 242 242 / var(--tw-bg-opacity, 1))}
        .transition-colors {
            transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }
        .text-red-800 {
            --tw-text-opacity: 1;
            color: rgb(153 27 27 / var(--tw-text-opacity, 1));
        }
        /* .text-red-800 {
            --tw-text-opacity: 1;
            color: rgb(220 38 38 / var(--tw-text-opacity, 1));
        } */

    </style>
@endpush
