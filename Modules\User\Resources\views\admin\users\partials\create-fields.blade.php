@php
    $default_role = config('asgard.user.config.default_role');
    $default_career = config('asgard.user.config.default_career');
@endphp
<div class="box box-default">
    <div class="box-body">
        <div class="nav-tabs-custom nav-tabs-{{ $skin_bootstrap or 'primary' }}">
            <ul class="nav nav-tabs">
                <li class="active">
                    <a href="#tab_data" data-toggle="tab">{{ _trans('user::users.tabs.data') }}</a>
                </li>
                <li class=" ">
                    <a href="#tab_approve" data-toggle="tab">{{ _trans('user::users.tabs.approve data') }}</a>
                </li>
                <li class=" ">
                    <a href="#tab_role" data-toggle="tab">{{ _trans('user::users.tabs.roles') }}</a>
                </li>
                <li class=" ">
                    <a href="#tab_permission" data-toggle="tab">{{ _trans('user::users.tabs.permissions') }}</a>
                </li>
            </ul>
            <div class="tab-content">
                <div class="tab-pane active" id="tab_data">
                    <div class="" style="padding-left: 100px">
                        <div class='form-group text-center' style="position: absolute; left: 20px;">
                            <label for="" style="margin-bottom: 20px;">รูปโปรไฟล์</label> <br>
                            @include('core::partials.custom-file-upload', [
                                'gallery' => 'avatar',
                                'name' => 'avatar',
                                'files' => [],
                            ])
                            @yield('custom-upload-avatar')
                        </div>
                        @include('user::admin.partials.profile', [
                            'model' => $item,
                            'user_career' => $default_career,
                        ])

                        <br>
                        <div class="row">
                            <div class='col-sm-12 form-group{{ $errors->has('password') ? ' has-error' : '' }}'>
                                {!! Form::label('password', _trans('user::users.form.password')) !!}
                                {!! Form::input('password', 'password', old('password'), ['class' => 'form-control']) !!}
                                {!! $errors->first('password', '<span class="help-block">:message</span>') !!}
                            </div>
                            <div
                                class='col-sm-12 form-group{{ $errors->has('password_confirmation') ? ' has-error' : '' }}'>
                                {!! Form::label('password_confirmation', _trans('user::users.form.password-confirmation')) !!}
                                {!! Form::input('password', 'password_confirmation', old('password_confirmation'), ['class' => 'form-control']) !!}
                                {!! $errors->first('password_confirmation', '<span class="help-block">:message</span>') !!}
                            </div>

                            <br>
                            <div class="col-sm-12 pt-4 pb-5">
                                <label class="zd-icheck zd-icheck-checkbox ">
                                    {{ _trans('user::users.form.is activated') }}
                                    <input type="hidden" name="activated" value="1"><input checked type="checkbox"
                                        onclick="this.previousSibling.value=1-this.previousSibling.value">
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="tab-pane " id="tab_approve">

                    @include('user::admin.partials.approve', [
                        'model' => $item,
                        'user_career' => $default_career,
                    ])

                    <div class="row">
                        <div class="col-sm-6">
                            <label for="verify_status">สถานะการตรวจสอบตัวตน</label>
                            <select name="verify_status" id="verify_status" class="form-control">
                                @foreach (config('asgard.user.data.verify_status', []) as $s => $vs)
                                    <option value="{{ $vs['value'] }}"
                                        {{ old('verify_status') == $vs['value'] ? 'selected' : '' }}>{{ $vs['label'] }}
                                    </option>
                                @endforeach
                            </select>
                            {{-- <div class="col-sm-12">
                            <label class="zd-icheck zd-icheck-checkbox ">
                                <input type="hidden" name="verify_status" value="{{(int)!!$item->verified_at}}"><input 
                                    {{$item->verified_at?'checked':''}} type="checkbox"
                                    onclick="this.previousSibling.value=1-this.previousSibling.value">
                                <span class="checkmark"></span>
                                {{ _trans('user::users.form.is approved') }}
                            </label> --}}
                        </div>
                    </div>
                </div>
                <div class="tab-pane " id="tab_role">
                    {{-- @dd($roles, $default_role, $roles->flip()->get($default_role)) --}}
                    <div class='form-group{{ $errors->has('roles[]') ? ' has-error' : '' }}'>
                        {!! Form::label('roles[]', _trans('user::users.form.roles')) !!}
                        {!! Form::select('roles[]', $roles->toArray(), old('roles[]', $roles->flip()->get($default_role)), [
                            'class' => 'zd-select2 form-control',
                            'style' => 'width:100%',
                            'multiple' => 'multiple',
                        ]) !!}
                        {!! $errors->first('roles[]', '<span class="help-block">:message</span>') !!}
                    </div>
                    <div class='form-group{{ $errors->has('expire_at') ? ' has-error' : '' }}'>
                        {!! Form::label('expire_at', _trans('user::users.form.expire at')) !!}
                        {!! Form::input(
                            'date',
                            'expire_at',
                            old(
                                'expire_at',
                                date(
                                    'Y-m-d',
                                    strtotime($item->roles[0]->pivot->expire_at ?? '+' . config('asgard.user.config.premuim_expire_time')),
                                ),
                            ),
                            ['class' => 'form-control'],
                        ) !!}
                        {!! $errors->first('expire_at', '<span class="help-block">:message</span>') !!}
                    </div>
                </div>
                <div class="tab-pane " id="tab_permission">
                    @include('user::admin.partials.permissions', ['model' => $item])
                </div>
            </div>
        </div>
    </div>
    <div class="box-footer">
        @include('core::components.button-submit')
        {{-- <button type="submit" --}}
        {{-- class="btn btn-{{ $skin_bootstrap or 'primary' }} btn-flat">{{ _trans('core::core.button.create') }}</button> --}}
        {{-- <a class="btn btn-danger pull-right btn-flat" href="{{ route('admin.user.user.index')}}"><i --}}
        {{-- class="fa fa-times"></i> {{ _trans('core::core.button.cancel') }}</a> --}}
    </div>
</div>
