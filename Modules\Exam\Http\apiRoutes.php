<?php

use Illuminate\Routing\Router;
/** @var Router $router */

$router->group(['prefix' => '/exam', 'middleware' => ['api.token']], function (Router $router) {

    $router->get('applicants/join', [
        'as' => 'api.exam.applicant.join',
        'uses' => 'ApplicantController@join',
        'middleware' => 'token-can:exam.applicants.join'
    ]); 
    $router->get('applicants/unjoin', [
        'as' => 'api.exam.applicant.unjoin',
        'uses' => 'ApplicantController@join',
        'middleware' => 'token-can:exam.applicants.join'
    ]);
    $router->post('applicants/storeAnswers', [
        'as' => 'api.exam.applicant.storeAnswers',
        'uses' => 'ApplicantController@storeAnswers',
        'middleware' => 'token-can:exam.applicants.join'
    ]);

    // Payment API routes (public access for authenticated users)
    $router->post('payment/generate-qr', [
        'as' => 'api.exam.payment.generate-qr',
        'uses' => 'PaymentController@generateQR',
    ]);
    $router->post('payment/check-status', [
        'as' => 'api.exam.payment.check-status',
        'uses' => 'PaymentController@checkStatus',
    ]);
    $router->get('qr-image/{data}', [
        'as' => 'api.exam.qr-image',
        'uses' => 'PaymentController@qrImage',
    ]);

});
$router->group(['prefix' => '/exam', 'middleware' => ['api.token', 'auth.admin']], function (Router $router) {
    $router->bind('exam', function ($id) {
        return app('Modules\Exam\Repositories\ExamRepository')->find($id);
    });
    // $router->bind('schedule', function ($id) {
    //     return app('Modules\Exam\Repositories\ExamRepository')->findSchedule($id);
    // });
    $router->bind('user', function ($id) {
        return app('Modules\User\Repositories\UserRepository')->find($id);
    });

    $router->group(['prefix' => '/{type}'], function (Router $router) {

        // for dataTable at backend
        $router->match(['get', 'post'], 'exams-server-side', [
            'as' => 'api.exam.exam.indexServerSide',
            'uses' => 'ExamController@indexServerSide',
            'middleware' => 'token-can:exam.exams.index',
        ]);

        $router->get('exams', [
            'as' => 'api.exam.exam.index',
            'uses' => 'ExamController@index',
            'middleware' => 'token-can:exam.exams.index'
        ]); 
        $router->post('exams/store', [
            'as' => 'api.exam.exam.store',
            'uses' => 'ExamController@store',
            'middleware' => 'token-can:exam.exams.create'
        ]); 
        $router->post('exams/mark-status', [
            'as' => 'api.exam.exam.mark-status',
            'uses' => 'ExamController@markStatus',
            'middleware' => 'token-can:exam.exams.edit'
        ]);
        $router->put('exams/{exam}/update', [
            'as' => 'api.exam.exam.update',
            'uses' => 'ExamController@update',
            'middleware' => 'token-can:exam.exams.edit'
        ]);
        $router->match(['delete', 'get'], 'exams/{exam}/destroy', [
            'as' => 'api.exam.exam.destroy',
            'uses' => 'ExamController@destroy',
            'middleware' => 'token-can:exam.exams.destroy'
        ]); 
        $router->match(['delete', 'patch'], 'bulk-destroy', [
            'as' => 'api.exam.exam.bulk-destroy',
            'uses' => 'ExamController@bulkDestroy',
            'middleware' => 'token-can:exam.exams.destroy',
        ]);
        $router->post('send-certificate/{applicant}', [
            'as' => 'api.exam.exam.send-certificate',
            'uses' => 'ExamController@sendCertificate',
            'middleware' => 'token-can:exam.exams.index'
        ]);  
        $router->post('disable-certificate/{applicant}', [
            'as' => 'api.exam.exam.disable-certificate',
            'uses' => 'ExamController@disableCertificate',
            'middleware' => 'token-can:exam.exams.index'
        ]);  
        $router->post('send-join-confirm/{applicant}', [
            'as' => 'api.exam.exam.send-join-confirm',
            'uses' => 'ExamController@sendJoinConfirm',
            'middleware' => 'token-can:exam.exams.index'
        ]);

        // $router->get('{schedule}/checkname-confirm/{userId}', [
        $router->get('{access_token}/checkname-confirm/{userId}', [
            'as' => 'api.exam.exam.checkname-confirm',
            'uses' => 'ExamController@checknameConfirm',
        ]);

        $router->get('{access_token}/checkname-confirm-member/{memberId}', [
            'as' => 'api.exam.exam.checkname-confirm-member',
            'uses' => 'ExamController@checknameConfirmMember',
        ]);

    });


    $router->bind('applicant', function ($id) {
        return app('Modules\Exam\Repositories\ApplicantRepository')->find($id);
    });
    
    
    // for dataTable at backend
    $router->match(['get', 'post'], 'applicants-server-side', [
        'as' => 'api.exam.applicant.indexServerSide',
        'uses' => 'ApplicantController@indexServerSide',
        'middleware' => 'token-can:exam.applicants.index',
    ]);

    $router->get('applicants', [
        'as' => 'api.exam.applicant.index',
        'uses' => 'ApplicantController@index',
        'middleware' => 'token-can:exam.applicants.index'
    ]); 
    $router->post('applicants/store', [
        'as' => 'api.exam.applicant.store',
        'uses' => 'ApplicantController@store',
        'middleware' => 'token-can:exam.applicants.create'
    ]);  
    // $router->post('applicants/bulk-join', [
    //     'as' => 'api.exam.applicant.bulk-join',
    //     'uses' => 'ApplicantController@bulkJoin',
    //     'middleware' => 'token-can:exam.applicants.create'
    // ]); 
    $router->put('applicants/{applicant}/update', [
        'as' => 'api.exam.applicant.update',
        'uses' => 'ApplicantController@update',
        'middleware' => 'token-can:exam.applicants.edit'
    ]);
    $router->post('applicants/update-track-shipping', [
        'as' => 'api.exam.applicant.updateTrackShipping',
        'uses' => 'ApplicantController@updateTrackShipping',
        'middleware' => 'token-can:exam.applicants.edit'
    ]);
    $router->post('applicants/update-cert-no', [
        'as' => 'api.exam.applicant.updateCertNo',
        'uses' => 'ApplicantController@updateCertNo',
        'middleware' => 'token-can:exam.applicants.edit'
    ]);
    $router->post('applicants/update-member-no', [
        'as' => 'api.exam.applicant.updateMemberNo',
        'uses' => 'ApplicantController@updateMemberNo',
        'middleware' => 'token-can:exam.applicants.edit'
    ]);
    $router->post('applicants/bulk-update-member-no', [
        'as' => 'api.exam.applicant.bulkUpdateMemberNo',
        'uses' => 'ApplicantController@bulkUpdateMemberNo',
        'middleware' => 'token-can:exam.applicants.edit'
    ]);
    $router->post('applicants/update-member-rating', [
        'as' => 'api.exam.applicant.updateMemberRating',
        'uses' => 'ApplicantController@updateMemberRating',
        'middleware' => 'token-can:exam.applicants.edit'
    ]);
    // $router->match(['delete', 'get'], 'applicants/{applicant}/destroy', [
    //     'as' => 'api.exam.applicant.destroy',
    //     'uses' => 'ApplicantController@destroy',
    //     'middleware' => 'token-can:exam.applicants.destroy'
    // ]);
    $router->match(['delete', 'get'], 'applicants/{schedule_ids}/{user_id}/destroy', [
        'as' => 'api.exam.applicant.destroy',
        'uses' => 'ApplicantController@destroy',
        'middleware' => 'token-can:exam.applicants.destroy'
    ]);
    $router->match(['delete', 'get'], 'applicants/{exam_id}/{user_id}/destroyAnswers', [
        'as' => 'api.exam.applicant.destroyAnswers',
        'uses' => 'ApplicantController@destroyAnswers',
        'middleware' => 'token-can:exam.applicants.destroy'
    ]);

    // Payment admin API routes
    $router->post('payment/confirm', [
        'as' => 'api.exam.payment.confirm',
        'uses' => 'PaymentController@confirmPayment',
        'middleware' => 'token-can:exam.applicants.edit'
    ]);

// append





});

// Public payment callback (no authentication required)
$router->post('exam/payment/callback', [
    'as' => 'api.exam.payment.callback',
    'uses' => 'PaymentController@callback',
]);
