@extends('layouts.master-v2')

@section('title')
    หัวข้อสอบ {{ $exam->translate()->title }}
@stop
@include('school::partials.modal-select-school', ['disable_custom_school' => true])
@section('content')

    <section class="exam-section section-b-space">
        <div class="container">
            @if ($v = $exam->files[0])
                <div class="card mb-4">
                    <div class="card-body p-0">
                        <div id="demo-test-gallery" class="demo-gallery row">
                            <a href="{{ $v->path }}" class="col-12"
                                data-size="{{ $v->fileinfo['width'] }}x{{ $v->fileinfo['height'] }}"
                                data-med="{{ $v->path }}"
                                data-med-size="{{ $v->fileinfo['width'] * 1.5 }}x{{ $v->fileinfo['height'] * 1.5 }}"
                                data-author="">
                                <img src="{{ $v->path }}" alt="" width="100%" class="rounded-lg" />
                            </a>
                        </div>
                    </div>
                </div>
            @endif
            <div class="card mb-4" id="exam_{{ $exam->id }}" data-questions_count="{{ $exam->questions_count }}"
                data-join_message="{{ $exam->translate()->join_message }}"
                data-join_confirm="{{ $exam->translate()->join_confirm }}"
                data-join_linkto="{{ $exam->translate()->join_linkto }}">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden shine-effect ">
                    <div class="chinese-pattern position-relative py-4">
                        <div class="position-absolute inset-0 top-0 start-0 end-0 bottom-0 bg-gradient-red opacity-90"></div>
                        <div class="position-relative text-center px-4">
                            {{-- <div class="chinese-symbol fs-1 fw-bold text-warning mb-2 floating">汉语水平考试</div> --}}
                            <h1 class="chinese-symbol fs-1 fw-bold text-warning mb-2 floating">汉语</h1> 
                            <p class="text-warning mt-2">ก้าวสู่ความสำเร็จในการเรียนรู้ภาษาจีน</p>
                            <h2 class="text-3xl md:text-4xl font-bold text-white">{!! nl2br($exam->translate()->title) !!}</h2>
                            <p class=" text-white mt-2">{!! nl2br($exam->translate()->summary) !!}</p>
                        </div>

                        <!-- Decorative elements -->
                        <div class="absolute top-4 left-4 text-white opacity-20 text-6xl chinese-symbol">学</div>
                        <div class="absolute bottom-4 right-4 text-white opacity-20 text-6xl chinese-symbol">习</div>
                    </div> 
                </div>

                <!-- Progress Steps -->
                <div class="px-4 pt-4">
                    <div class="d-flex align-items-center justify-content-between mb-5 position-relative">
                        <div class="d-flex flex-column align-items-center">
                            <div id="step1" class="progress-step active" data-step="0">1</div>
                            <span class="mt-2 small">ข้อมูลส่วนตัว</span>
                        </div>
                        <div id="line1" class="progress-line"></div>
                        <div class="d-flex flex-column align-items-center">
                            <div id="step2" class="progress-step" data-step="1">2</div>
                            <span class="mt-2 small">เลือกวันสอบ</span>
                        </div>
                        <div id="line2" class="progress-line"></div>
                        <div class="d-flex flex-column align-items-center">
                            <div id="step3" class="progress-step" data-step="2">3</div>
                            <span class="mt-2 small">เลือกศูนย์สอบ</span>
                        </div>
                        <div id="line3" class="progress-line"></div>
                        <div class="d-flex flex-column align-items-center">
                            <div id="step4" class="progress-step" data-step="3">4</div>
                            <span class="mt-2 small">ยืนยันข้อมูล</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-12 col-12">

                    @if($exam->translate()->page_body != "")
                        <div class="card mb-4">
                            <div id="page_body" class="card-body">
                                {!! $exam->translate()->page_body !!}
                            </div>
                        </div>
                    @endif

                    <div class="bg-white rounded-lg shadow-lg overflow-hidden  mb-4 d-none"> 
                        <div class="p-3">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-2xl font-semibold text-gray-800">ทำไมต้องสอบ HSK?</h3>
                                {{-- <div class="w-10 h-10 bg-red-50 icon-rounded rounded-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div> --}}
                            </div>
                            
                            <div class="grid grid-cols-1 md-grid-cols-2 gap-4 mb-4">
                                <div class="bg-gradient-to-br from-red-50 to-white p-4 rounded-lg border border-red-100 shadow-sm transform transition-transform hover-scale-105">
                                    <div class="flex items-start">
                                        <div class="bg-red-500 icon-rounded rounded-full p-2 mr-3 text-white">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">มาตรฐานสากล</h4>
                                            <p class="text-sm text-gray-600">เป็นการสอบที่ได้รับการยอมรับทั่วโลก</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-gradient-to-br from-red-50 to-white p-4 rounded-lg border border-red-100 shadow-sm transform transition-transform hover-scale-105">
                                    <div class="flex items-start">
                                        <div class="bg-red-500 icon-rounded rounded-full p-2 mr-3 text-white">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">โอกาสการศึกษา</h4>
                                            <p class="text-sm text-gray-600">เพิ่มโอกาสในการศึกษาต่อที่ประเทศจีน</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-gradient-to-br from-red-50 to-white p-4 rounded-lg border border-red-100 shadow-sm transform transition-transform hover-scale-105">
                                    <div class="flex items-start">
                                        <div class="bg-red-500 icon-rounded rounded-full p-2 mr-3 text-white">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">โอกาสการทำงาน</h4>
                                            <p class="text-sm text-gray-600">เพิ่มโอกาสในการทำงานกับบริษัทจีน</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-gradient-to-br from-red-50 to-white p-4 rounded-lg border border-red-100 shadow-sm transform transition-transform hover-scale-105">
                                    <div class="flex items-start">
                                        <div class="bg-red-500 icon-rounded rounded-full p-2 mr-3 text-white">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-gray-800">ประเมินความก้าวหน้า</h4>
                                            <p class="text-sm text-gray-600">วัดระดับความสามารถทางภาษาจีนของตนเอง</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-red-50 border border-red-100 rounded-lg p-5 mb-4">
                                <h4 class="font-semibold text-red-800 mb-3 flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                                    </svg>
                                    ระดับการสอบ HSK
                                </h4>
                                <div class="grid grid-cols-1 md-grid-cols-3 gap-2 text-center">
                                    <div class="bg-white p-3 rounded-lg border border-red-200 shadow-sm transform transition-transform hover-scale-105">
                                        <div class="font-bold text-red-800 text-lg">HSK 1-2</div>
                                        <div class="text-sm text-gray-600">ระดับพื้นฐาน</div>
                                        <div class="text-xs text-gray-500 mt-1">150-300 คำศัพท์</div>
                                    </div>
                                    <div class="bg-white p-3 rounded-lg border border-red-200 shadow-sm transform transition-transform hover-scale-105">
                                        <div class="font-bold text-red-800 text-lg">HSK 3-4</div>
                                        <div class="text-sm text-gray-600">ระดับกลาง</div>
                                        <div class="text-xs text-gray-500 mt-1">600-1,200 คำศัพท์</div>
                                    </div>
                                    <div class="bg-white p-3 rounded-lg border border-red-200 shadow-sm transform transition-transform hover-scale-105">
                                        <div class="font-bold text-red-800 text-lg">HSK 5-6</div>
                                        <div class="text-sm text-gray-600">ระดับสูง</div>
                                        <div class="text-xs text-gray-500 mt-1">2,500-5,000 คำศัพท์</div>
                                    </div>
                                </div>
                            </div>

                            {{-- <div class="bg-yellow-50 border border-yellow-100 rounded-lg p-5">
                                <h4 class="font-semibold text-yellow-800 mb-3 flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                    กำหนดการสอบครั้งถัดไป
                                </h4>
                                <div class="grid grid-cols-1 md-grid-cols-2 gap-4">
                                    <div class="bg-white p-3 rounded-lg border border-yellow-200 shadow-sm">
                                        <div class="font-semibold text-yellow-800">15 ธันวาคม 2566</div>
                                        <div class="text-sm text-gray-600">ปิดรับสมัคร: 30 พฤศจิกายน 2566</div>
                                        <div class="mt-1 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded inline-block">เปิดรับสมัครแล้ว</div>
                                    </div>
                                    <div class="bg-white p-3 rounded-lg border border-yellow-200 shadow-sm">
                                        <div class="font-semibold text-yellow-800">25 กุมภาพันธ์ 2567</div>
                                        <div class="text-sm text-gray-600">ปิดรับสมัคร: 10 กุมภาพันธ์ 2567</div>
                                        <div class="mt-1 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded inline-block">เร็วๆ นี้</div>
                                    </div>
                                </div>
                            </div> --}}
                        </div>
                    </div>
                </div>
                <div class="col-lg-12 col-12">
                    <div class="card d-none">
                        <div class="card-body">
                            <h4>ตารางสอบ</h4>
                            <table class="table mt-3">
                                @foreach ($exam->schedules->sortBy("no") as $s => $schedule)
                                    <tr>
                                        <td id="schedule_title_{{ $schedule->id }}">
                                            @if ($schedule->no != '')
                                                {{-- ศูนย์สอบที่ --}}
                                                <h4 class="custom-font-pink-light mb-0">
                                                    {{ !empty($schedule->no_alias) ? $schedule->no_alias : 'ศูนย์สอบที่ ' . $schedule->no }}
                                                </h4>
                                            @endif
                                        </td>
                                        <td id="schedule_time_{{ $schedule->id }}">
                                            @if ($schedule->no != '')
                                                {{ _date_thai('D j M y', $schedule->date) }}

                                                @if (!$schedule->status)
                                                    <h5 class="text-danger text-nowrap mb-0 ml-2 d-inline-block">
                                                        << ปิดการรับสมัครแล้ว</h5>
                                                @endif 
                                            @endif
                                        </td> 
                                    </tr>
                                    <tr>
                                        <td colspan="3" style="border-top: none" class="pt-0">
                                            {{ $schedule->location }}</td>
                                    </tr>
                                @endforeach
                            </table>
                        </div>
                    </div>
                    <div class="card"> 
                        <div class="card-body">
                            <h4>แบบฟอร์มสมัครสอบ</h4>
                            {!! Form::open([
                                'route' => ['exam.join', [$type, $exam->access_token]],
                                'method' => 'post',
                                'enctype' => 'multipart/form-data',
                                'id' => 'hskRegistrationForm',
                            ]) !!} 

                            <!-- Step 1: Personal Information -->
                            <div id="step1Content" class="step-content active fade-in px-4 pb-4">
                                <h3 class="fs-5 fw-semibold text-dark mb-4 d-flex align-items-center">
                                    <svg class="me-2" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    ข้อมูลส่วนตัว
                                </h3>
                                
                                <div class="mb-4">
                                    <!-- Name in English -->
                                    <div class="input-animated bg-white rounded shadow-sm border p-3 mb-3">
                                        <div class="row">
                                            <div class="form-group col-4">
                                                <label for="pre_name_th">คำนำหน้าชื่อ (TH) *</label>
                                                <select name="pre_name_th" id="pre_name_th" class="form-control" required>
                                                    @foreach (config('asgard.exam.data.pre_name_th') as $p)
                                                        <option {{ auth()->user()->pre_name == $p ? 'selected' : '' }}
                                                            value="{{ $p }}">{{ $p }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="form-group col-4">
                                                <label for="first_name_th">ชื่อ (TH) *</label>
                                                <input type="text" name="first_name_th" id="first_name_th"
                                                    class="form-control" required value="{{ auth()->user()->first_name }}">
                                            </div>
                                            <div class="form-group col-4">
                                                <label for="last_name_th">นามสกุล (TH) *</label>
                                                <input type="text" name="last_name_th" id="last_name_th"
                                                    class="form-control" required value="{{ auth()->user()->last_name }}">
                                            </div>
                                        </div>
                                        {{-- <label for="nameEn" class="form-label small fw-medium text-secondary mb-1">ชื่อ-นามสกุล (ภาษาอังกฤษ)</label>
                                        <input type="text" id="nameEn" name="nameEn" class="form-control" placeholder="Name Surname"> --}}
                                    </div>
                                    
                                    <!-- Name in Thai -->
                                    <div class="input-animated bg-white rounded shadow-sm border p-3 mb-3">
                                        <label for="nameTh" class="form-label small fw-medium text-secondary mb-1">ชื่อ-นามสกุล (ภาษาไทย)</label>
                                        <input type="text" id="nameTh" name="nameTh" class="form-control" placeholder="ชื่อ นามสกุล">
                                    </div>
                                    
                                    <!-- ID Card Number -->
                                    <div class="input-animated bg-white rounded shadow-sm border p-3 mb-3">
                                        <label for="idCard" class="form-label small fw-medium text-secondary mb-1">เลขบัตรประชาชน</label>
                                        <input type="text" id="idCard" name="idCard" class="form-control" placeholder="X-XXXX-XXXXX-XX-X" maxlength="13">
                                        <p class="form-text mt-1">กรุณากรอกเลข 13 หลักโดยไม่มีขีด</p>
                                    </div>
                                    
                                    <!-- School -->
                                    <div class="input-animated bg-white rounded shadow-sm border p-3 mb-3">
                                        <label for="school" class="form-label small fw-medium text-secondary mb-1">โรงเรียน/สถาบันการศึกษา</label>
                                        <input type="text" id="school" name="school" class="form-control" placeholder="ชื่อโรงเรียน/สถาบัน">
                                    </div>
                                    
                                    <!-- HSK Level -->
                                    <div class="bg-white rounded shadow-sm border p-3 mb-3">
                                        <label class="form-label small fw-medium text-secondary mb-2">ระดับการสอบ</label>
                                        <div class="row row-cols-2 row-cols-md-3 g-2">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="hskLevel" id="hsk1" value="1">
                                                    <label class="form-check-label custom-radio-card" for="hsk1">HSK 1</label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="hskLevel" id="hsk2" value="2">
                                                    <label class="form-check-label custom-radio-card" for="hsk2">HSK 2</label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="hskLevel" id="hsk3" value="3">
                                                    <label class="form-check-label custom-radio-card" for="hsk3">HSK 3</label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="hskLevel" id="hsk4" value="4">
                                                    <label class="form-check-label custom-radio-card" for="hsk4">HSK 4</label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="hskLevel" id="hsk5" value="5">
                                                    <label class="form-check-label custom-radio-card" for="hsk5">HSK 5</label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="hskLevel" id="hsk6" value="6">
                                                    <label class="form-check-label custom-radio-card" for="hsk6">HSK 6</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Contact Information -->
                                    <div class="row g-3">
                                        <div class="col-12 col-md-6">
                                            <div class="input-animated bg-white rounded shadow-sm border p-3">
                                                <label for="email" class="form-label small fw-medium text-secondary mb-1">อีเมล</label>
                                                <input type="email" id="email" name="email" class="form-control" placeholder="<EMAIL>">
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-6">
                                            <div class="input-animated bg-white rounded shadow-sm border p-3">
                                                <label for="phone" class="form-label small fw-medium text-secondary mb-1">เบอร์โทรศัพท์</label>
                                                <input type="tel" id="phone" name="phone" class="form-control" placeholder="0xxxxxxxxx">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Next Button -->
                                <div class="mt-4 d-flex justify-content-between align-items-center">
                                    <button type="button" class="btn-skip" id="step1Skip">ข้ามขั้นตอนนี้</button>
                                    <button type="button" id="step1Next" class="btn btn-danger py-2 px-4 d-flex align-items-center">
                                        <span>ถัดไป</span>
                                        <svg class="ms-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Step 2: Exam Date Selection -->
                            <div id="step2Content" class="step-content fade-in px-4 pb-4">
                                <h3 class="fs-5 fw-semibold text-dark mb-4 d-flex align-items-center">
                                    <svg class="me-2" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    เลือกวันที่สอบ
                                </h3>
                                
                                <div class="mb-4">
                                    <!-- Month Selection -->
                                    <div class="bg-white rounded shadow-sm border p-3 mb-4">
                                        <h4 class="fw-medium text-dark mb-3">เดือนที่เปิดสอบ</h4>
                                        <div class="row row-cols-2 row-cols-md-4 g-3">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examMonth" id="december" value="december">
                                                    <label class="form-check-label custom-radio-card text-center" for="december">
                                                        <div class="fw-medium">ธันวาคม</div>
                                                        <div class="small text-secondary">2566</div>
                                                        <div class="mt-2 badge bg-success-subtle text-success">เปิดรับสมัคร</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examMonth" id="february" value="february">
                                                    <label class="form-check-label custom-radio-card text-center" for="february">
                                                        <div class="fw-medium">กุมภาพันธ์</div>
                                                        <div class="small text-secondary">2567</div>
                                                        <div class="mt-2 badge bg-success-subtle text-success">เปิดรับสมัคร</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examMonth" id="april" value="april">
                                                    <label class="form-check-label custom-radio-card text-center" for="april">
                                                        <div class="fw-medium">เมษายน</div>
                                                        <div class="small text-secondary">2567</div>
                                                        <div class="mt-2 badge bg-secondary-subtle text-secondary">เร็วๆ นี้</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examMonth" id="june" value="june">
                                                    <label class="form-check-label custom-radio-card text-center" for="june">
                                                        <div class="fw-medium">มิถุนายน</div>
                                                        <div class="small text-secondary">2567</div>
                                                        <div class="mt-2 badge bg-secondary-subtle text-secondary">เร็วๆ นี้</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Date Selection - December -->
                                    <div id="decemberDates" class="bg-white rounded shadow-sm border p-3 mb-4 d-none">
                                        <h4 class="fw-medium text-dark mb-3">วันที่เปิดสอบ (ธันวาคม 2566)</h4>
                                        <div class="row row-cols-2 row-cols-md-3 g-3">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="dec15" value="2023-12-15">
                                                    <label class="form-check-label custom-radio-card text-center" for="dec15">
                                                        <div class="fs-4 fw-bold">15</div>
                                                        <div class="small text-secondary">วันศุกร์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="dec16" value="2023-12-16">
                                                    <label class="form-check-label custom-radio-card text-center" for="dec16">
                                                        <div class="fs-4 fw-bold">16</div>
                                                        <div class="small text-secondary">วันเสาร์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="dec17" value="2023-12-17">
                                                    <label class="form-check-label custom-radio-card text-center" for="dec17">
                                                        <div class="fs-4 fw-bold">17</div>
                                                        <div class="small text-secondary">วันอาทิตย์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Date Selection - February -->
                                    <div id="februaryDates" class="bg-white rounded shadow-sm border p-3 mb-4 d-none">
                                        <h4 class="fw-medium text-dark mb-3">วันที่เปิดสอบ (กุมภาพันธ์ 2567)</h4>
                                        <div class="row row-cols-2 row-cols-md-3 g-3">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="feb23" value="2024-02-23">
                                                    <label class="form-check-label custom-radio-card text-center" for="feb23">
                                                        <div class="fs-4 fw-bold">23</div>
                                                        <div class="small text-secondary">วันศุกร์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="feb24" value="2024-02-24">
                                                    <label class="form-check-label custom-radio-card text-center" for="feb24">
                                                        <div class="fs-4 fw-bold">24</div>
                                                        <div class="small text-secondary">วันเสาร์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="feb25" value="2024-02-25">
                                                    <label class="form-check-label custom-radio-card text-center" for="feb25">
                                                        <div class="fs-4 fw-bold">25</div>
                                                        <div class="small text-secondary">วันอาทิตย์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Date Selection - April -->
                                    <div id="aprilDates" class="bg-white rounded shadow-sm border p-3 mb-4 d-none">
                                        <h4 class="fw-medium text-dark mb-3">วันที่เปิดสอบ (เมษายน 2567)</h4>
                                        <div class="row row-cols-2 row-cols-md-3 g-3">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="apr19" value="2024-04-19">
                                                    <label class="form-check-label custom-radio-card text-center" for="apr19">
                                                        <div class="fs-4 fw-bold">19</div>
                                                        <div class="small text-secondary">วันศุกร์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="apr20" value="2024-04-20">
                                                    <label class="form-check-label custom-radio-card text-center" for="apr20">
                                                        <div class="fs-4 fw-bold">20</div>
                                                        <div class="small text-secondary">วันเสาร์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="apr21" value="2024-04-21">
                                                    <label class="form-check-label custom-radio-card text-center" for="apr21">
                                                        <div class="fs-4 fw-bold">21</div>
                                                        <div class="small text-secondary">วันอาทิตย์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Date Selection - June -->
                                    <div id="juneDates" class="bg-white rounded shadow-sm border p-3 mb-4 d-none">
                                        <h4 class="fw-medium text-dark mb-3">วันที่เปิดสอบ (มิถุนายน 2567)</h4>
                                        <div class="row row-cols-2 row-cols-md-3 g-3">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="jun14" value="2024-06-14">
                                                    <label class="form-check-label custom-radio-card text-center" for="jun14">
                                                        <div class="fs-4 fw-bold">14</div>
                                                        <div class="small text-secondary">วันศุกร์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="jun15" value="2024-06-15">
                                                    <label class="form-check-label custom-radio-card text-center" for="jun15">
                                                        <div class="fs-4 fw-bold">15</div>
                                                        <div class="small text-secondary">วันเสาร์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="examDate" id="jun16" value="2024-06-16">
                                                    <label class="form-check-label custom-radio-card text-center" for="jun16">
                                                        <div class="fs-4 fw-bold">16</div>
                                                        <div class="small text-secondary">วันอาทิตย์</div>
                                                        <div class="small text-secondary">09:00 - 12:00 น.</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Navigation Buttons -->
                                <div class="d-flex justify-content-between mt-4">
                                    <div>
                                        <button type="button" id="step2Prev" class="btn btn-light border py-2 px-4 d-flex align-items-center">
                                            <svg class="me-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"></path>
                                            </svg>
                                            <span>ย้อนกลับ</span>
                                        </button>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <button type="button" class="btn-skip me-3" id="step2Skip">ข้ามขั้นตอนนี้</button>
                                        <button type="button" id="step2Next" class="btn btn-danger py-2 px-4 d-flex align-items-center">
                                            <span>ถัดไป</span>
                                            <svg class="ms-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Test Center Selection -->
                            <div id="step3Content" class="step-content fade-in px-4 pb-4">
                                <h3 class="fs-5 fw-semibold text-dark mb-4 d-flex align-items-center">
                                    <svg class="me-2" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    เลือกศูนย์สอบ
                                </h3>
                                
                                <div class="mb-4">
                                    <!-- Region Selection -->
                                    <div class="bg-white rounded shadow-sm border p-3 mb-4">
                                        <h4 class="fw-medium text-dark mb-3">เลือกภูมิภาค</h4>
                                        <div class="row row-cols-2 row-cols-md-3 g-3">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="region" id="central" value="central">
                                                    <label class="form-check-label custom-radio-card text-center" for="central">
                                                        <div class="fw-medium">ภาคกลาง</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="region" id="north" value="north">
                                                    <label class="form-check-label custom-radio-card text-center" for="north">
                                                        <div class="fw-medium">ภาคเหนือ</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="region" id="northeast" value="northeast">
                                                    <label class="form-check-label custom-radio-card text-center" for="northeast">
                                                        <div class="fw-medium">ภาคตะวันออกเฉียงเหนือ</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="region" id="east" value="east">
                                                    <label class="form-check-label custom-radio-card text-center" for="east">
                                                        <div class="fw-medium">ภาคตะวันออก</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="region" id="west" value="west">
                                                    <label class="form-check-label custom-radio-card text-center" for="west">
                                                        <div class="fw-medium">ภาคตะวันตก</div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="region" id="south" value="south">
                                                    <label class="form-check-label custom-radio-card text-center" for="south">
                                                        <div class="fw-medium">ภาคใต้</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Centers - Central -->
                                    <div id="centralCenters" class="d-none mb-4">
                                        <h4 class="fw-medium text-dark mb-3">ศูนย์สอบในภาคกลาง</h4>
                                        <div class="row row-cols-1 row-cols-md-2 g-4">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="testCenter" id="bkk1" value="bkk1">
                                                    <label class="form-check-label center-card" for="bkk1">
                                                        <div class="center-image">
                                                            <div class="text-center p-3 bg-white bg-opacity-90">
                                                                <div class="fs-5 fw-bold text-danger chinese-symbol">孔子学院</div>
                                                                <div class="small">สถาบันขงจื่อ</div>
                                                            </div>
                                                        </div>
                                                        <div class="p-3">
                                                            <h5 class="fw-medium">สถาบันขงจื่อ จุฬาลงกรณ์มหาวิทยาลัย</h5>
                                                            <p class="small text-secondary mt-1">ถนนพญาไท แขวงวังใหม่ เขตปทุมวัน กรุงเทพฯ</p>
                                                            <div class="d-flex align-items-center mt-2 small text-secondary">
                                                                <svg class="me-1" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span>เวลาสอบ: 09:00 - 12:00 น.</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="testCenter" id="bkk2" value="bkk2">
                                                    <label class="form-check-label center-card" for="bkk2">
                                                        <div class="center-image">
                                                            <div class="text-center p-3 bg-white bg-opacity-90">
                                                                <div class="fs-5 fw-bold text-danger chinese-symbol">泰国大学</div>
                                                                <div class="small">มหาวิทยาลัยธรรมศาสตร์</div>
                                                            </div>
                                                        </div>
                                                        <div class="p-3">
                                                            <h5 class="fw-medium">มหาวิทยาลัยธรรมศาสตร์</h5>
                                                            <p class="small text-secondary mt-1">ถนนพระจันทร์ แขวงพระบรมมหาราชวัง เขตพระนคร กรุงเทพฯ</p>
                                                            <div class="d-flex align-items-center mt-2 small text-secondary">
                                                                <svg class="me-1" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span>เวลาสอบ: 09:00 - 12:00 น.</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Centers - North -->
                                    <div id="northCenters" class="d-none mb-4">
                                        <h4 class="fw-medium text-dark mb-3">ศูนย์สอบในภาคเหนือ</h4>
                                        <div class="row row-cols-1 row-cols-md-2 g-4">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="testCenter" id="chiangmai" value="chiangmai">
                                                    <label class="form-check-label center-card" for="chiangmai">
                                                        <div class="center-image">
                                                            <div class="text-center p-3 bg-white bg-opacity-90">
                                                                <div class="fs-5 fw-bold text-danger chinese-symbol">清迈大学</div>
                                                                <div class="small">มหาวิทยาลัยเชียงใหม่</div>
                                                            </div>
                                                        </div>
                                                        <div class="p-3">
                                                            <h5 class="fw-medium">มหาวิทยาลัยเชียงใหม่</h5>
                                                            <p class="small text-secondary mt-1">ถนนห้วยแก้ว ตำบลสุเทพ อำเภอเมือง จังหวัดเชียงใหม่</p>
                                                            <div class="d-flex align-items-center mt-2 small text-secondary">
                                                                <svg class="me-1" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span>เวลาสอบ: 09:00 - 12:00 น.</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Centers - Northeast -->
                                    <div id="northeastCenters" class="d-none mb-4">
                                        <h4 class="fw-medium text-dark mb-3">ศูนย์สอบในภาคตะวันออกเฉียงเหนือ</h4>
                                        <div class="row row-cols-1 row-cols-md-2 g-4">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="testCenter" id="khonkaen" value="khonkaen">
                                                    <label class="form-check-label center-card" for="khonkaen">
                                                        <div class="center-image">
                                                            <div class="text-center p-3 bg-white bg-opacity-90">
                                                                <div class="fs-5 fw-bold text-danger chinese-symbol">孔敬大学</div>
                                                                <div class="small">มหาวิทยาลัยขอนแก่น</div>
                                                            </div>
                                                        </div>
                                                        <div class="p-3">
                                                            <h5 class="fw-medium">มหาวิทยาลัยขอนแก่น</h5>
                                                            <p class="small text-secondary mt-1">ถนนมิตรภาพ ตำบลในเมือง อำเภอเมือง จังหวัดขอนแก่น</p>
                                                            <div class="d-flex align-items-center mt-2 small text-secondary">
                                                                <svg class="me-1" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span>เวลาสอบ: 09:00 - 12:00 น.</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Centers - East -->
                                    <div id="eastCenters" class="d-none mb-4">
                                        <h4 class="fw-medium text-dark mb-3">ศูนย์สอบในภาคตะวันออก</h4>
                                        <div class="row row-cols-1 row-cols-md-2 g-4">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="testCenter" id="chonburi" value="chonburi">
                                                    <label class="form-check-label center-card" for="chonburi">
                                                        <div class="center-image">
                                                            <div class="text-center p-3 bg-white bg-opacity-90">
                                                                <div class="fs-5 fw-bold text-danger chinese-symbol">布拉帕大学</div>
                                                                <div class="small">มหาวิทยาลัยบูรพา</div>
                                                            </div>
                                                        </div>
                                                        <div class="p-3">
                                                            <h5 class="fw-medium">มหาวิทยาลัยบูรพา</h5>
                                                            <p class="small text-secondary mt-1">ถนนลงหาดบางแสน ตำบลแสนสุข อำเภอเมือง จังหวัดชลบุรี</p>
                                                            <div class="d-flex align-items-center mt-2 small text-secondary">
                                                                <svg class="me-1" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span>เวลาสอบ: 09:00 - 12:00 น.</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Centers - West -->
                                    <div id="westCenters" class="d-none mb-4">
                                        <h4 class="fw-medium text-dark mb-3">ศูนย์สอบในภาคตะวันตก</h4>
                                        <div class="row row-cols-1 row-cols-md-2 g-4">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="testCenter" id="kanchanaburi" value="kanchanaburi">
                                                    <label class="form-check-label center-card" for="kanchanaburi">
                                                        <div class="center-image">
                                                            <div class="text-center p-3 bg-white bg-opacity-90">
                                                                <div class="fs-5 fw-bold text-danger chinese-symbol">北碧皇家大学</div>
                                                                <div class="small">มหาวิทยาลัยราชภัฏกาญจนบุรี</div>
                                                            </div>
                                                        </div>
                                                        <div class="p-3">
                                                            <h5 class="fw-medium">มหาวิทยาลัยราชภัฏกาญจนบุรี</h5>
                                                            <p class="small text-secondary mt-1">ตำบลหนองบัว อำเภอเมือง จังหวัดกาญจนบุรี</p>
                                                            <div class="d-flex align-items-center mt-2 small text-secondary">
                                                                <svg class="me-1" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span>เวลาสอบ: 09:00 - 12:00 น.</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Test Centers - South -->
                                    <div id="southCenters" class="d-none mb-4">
                                        <h4 class="fw-medium text-dark mb-3">ศูนย์สอบในภาคใต้</h4>
                                        <div class="row row-cols-1 row-cols-md-2 g-4">
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="testCenter" id="songkhla" value="songkhla">
                                                    <label class="form-check-label center-card" for="songkhla">
                                                        <div class="center-image">
                                                            <div class="text-center p-3 bg-white bg-opacity-90">
                                                                <div class="fs-5 fw-bold text-danger chinese-symbol">宋卡王子大学</div>
                                                                <div class="small">มหาวิทยาลัยสงขลานครินทร์</div>
                                                            </div>
                                                        </div>
                                                        <div class="p-3">
                                                            <h5 class="fw-medium">มหาวิทยาลัยสงขลานครินทร์</h5>
                                                            <p class="small text-secondary mt-1">ถนนกาญจนวณิชย์ อำเภอหาดใหญ่ จังหวัดสงขลา</p>
                                                            <div class="d-flex align-items-center mt-2 small text-secondary">
                                                                <svg class="me-1" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span>เวลาสอบ: 09:00 - 12:00 น.</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="testCenter" id="phuket" value="phuket">
                                                    <label class="form-check-label center-card" for="phuket">
                                                        <div class="center-image">
                                                            <div class="text-center p-3 bg-white bg-opacity-90">
                                                                <div class="fs-5 fw-bold text-danger chinese-symbol">普吉皇家大学</div>
                                                                <div class="small">มหาวิทยาลัยราชภัฏภูเก็ต</div>
                                                            </div>
                                                        </div>
                                                        <div class="p-3">
                                                            <h5 class="fw-medium">มหาวิทยาลัยราชภัฏภูเก็ต</h5>
                                                            <p class="small text-secondary mt-1">ถนนเทพกระษัตรี ตำบลรัษฎา อำเภอเมือง จังหวัดภูเก็ต</p>
                                                            <div class="d-flex align-items-center mt-2 small text-secondary">
                                                                <svg class="me-1" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                <span>เวลาสอบ: 09:00 - 12:00 น.</span>
                                                            </div>
                                                        </div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Navigation Buttons -->
                                <div class="d-flex justify-content-between mt-4">
                                    <div>
                                        <button type="button" id="step3Prev" class="btn btn-light border py-2 px-4 d-flex align-items-center">
                                            <svg class="me-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"></path>
                                            </svg>
                                            <span>ย้อนกลับ</span>
                                        </button>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <button type="button" class="btn-skip me-3" id="step3Skip">ข้ามขั้นตอนนี้</button>
                                        <button type="button" id="step3Next" class="btn btn-danger py-2 px-4 d-flex align-items-center">
                                            <span>ถัดไป</span>
                                            <svg class="ms-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4: Confirmation -->
                            <div id="step4Content" class="step-content fade-in px-4 pb-4">
                                <h3 class="fs-5 fw-semibold text-dark mb-4 d-flex align-items-center">
                                    <svg class="me-2" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    ยืนยันข้อมูลการสมัคร
                                </h3>
                                
                                <div class="mb-4">
                                    <div class="bg-white rounded shadow-sm border p-4 mb-4">
                                        <h4 class="fw-medium text-dark mb-3 d-flex align-items-center">
                                            <svg class="me-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                            ข้อมูลส่วนตัว
                                        </h4>
                                        <div class="row g-3">
                                            <div class="col-12 col-md-6">
                                                <p class="small text-secondary mb-1">ชื่อ-นามสกุล (ภาษาอังกฤษ)</p>
                                                <p id="confirmNameEn" class="fw-medium">-</p>
                                            </div>
                                            <div class="col-12 col-md-6">
                                                <p class="small text-secondary mb-1">ชื่อ-นามสกุล (ภาษาไทย)</p>
                                                <p id="confirmNameTh" class="fw-medium">-</p>
                                            </div>
                                            <div class="col-12 col-md-6">
                                                <p class="small text-secondary mb-1">เลขบัตรประชาชน</p>
                                                <p id="confirmIdCard" class="fw-medium">-</p>
                                            </div>
                                            <div class="col-12 col-md-6">
                                                <p class="small text-secondary mb-1">โรงเรียน/สถาบันการศึกษา</p>
                                                <p id="confirmSchool" class="fw-medium">-</p>
                                            </div>
                                            <div class="col-12 col-md-6">
                                                <p class="small text-secondary mb-1">อีเมล</p>
                                                <p id="confirmEmail" class="fw-medium">-</p>
                                            </div>
                                            <div class="col-12 col-md-6">
                                                <p class="small text-secondary mb-1">เบอร์โทรศัพท์</p>
                                                <p id="confirmPhone" class="fw-medium">-</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-white rounded shadow-sm border p-4 mb-4">
                                        <h4 class="fw-medium text-dark mb-3 d-flex align-items-center">
                                            <svg class="me-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                            </svg>
                                            ข้อมูลการสอบ
                                        </h4>
                                        <div class="row g-3">
                                            <div class="col-12 col-md-6">
                                                <p class="small text-secondary mb-1">ระดับการสอบ</p>
                                                <p id="confirmHskLevel" class="fw-medium">-</p>
                                            </div>
                                            <div class="col-12 col-md-6">
                                                <p class="small text-secondary mb-1">วันที่สอบ</p>
                                                <p id="confirmExamDate" class="fw-medium">-</p>
                                            </div>
                                            <div class="col-12">
                                                <p class="small text-secondary mb-1">ศูนย์สอบ</p>
                                                <p id="confirmTestCenter" class="fw-medium">-</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Terms and Conditions -->
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="terms" name="terms">
                                        <label class="form-check-label small" for="terms">
                                            ข้าพเจ้ายอมรับ<a href="#" class="text-danger">เงื่อนไขและข้อตกลง</a>ในการสมัครสอบ HSK และขอรับรองว่าข้อมูลที่กรอกเป็นความจริงทุกประการ
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Navigation Buttons -->
                                <div class="d-flex justify-content-between mt-4">
                                    <button type="button" id="step4Prev" class="btn btn-light border py-2 px-4 d-flex align-items-center">
                                        <svg class="me-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"></path>
                                        </svg>
                                        <span>ย้อนกลับ</span>
                                    </button>
                                    <button type="submit" id="submitBtn" class="btn btn-danger py-2 px-4 d-flex align-items-center pulse">
                                        <svg class="me-2" width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span>ยืนยันการสมัคร</span>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Step 5: Payment -->
                            <div id="step5Content" class="step-content fade-in px-4 pb-4"></div>

                            {!! Form::close() !!}
                        </div> 
                    </div>

                </div>

            </div>
        </div>
    </section>

@stop
@include('exam::public.partials.exam-script')
@include('core::partials.photoswipe')
@push('js-stack') 
<script src="{{ _asset('modules/core/vendor/select2/4.1.0-rc.0/select2.min.js') }}"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();  


        // Step Navigation
        const steps = ['step1Content', 'step2Content', 'step3Content', 'step4Content'];
        const stepIndicators = ['step1', 'step2', 'step3', 'step4'];
        const progressLines = ['line1', 'line2', 'line3'];
        let currentStep = 0;
        
        // Track step completion status
        const stepStatus = {
            0: false, // Step 1 incomplete by default
            1: false, // Step 2 incomplete by default
            2: false, // Step 3 incomplete by default
            3: false  // Step 4 incomplete by default
        };
        
        // Show a specific step
        function showStep(stepIndex) {
            // Hide all steps
            $('.step-content').removeClass('active');
            
            // Show the current step
            $('#' + steps[stepIndex]).addClass('active');
            
            // Update step indicators
            $.each(stepIndicators, function(index, indicator) {
                const $element = $('#' + indicator);
                if (index < stepIndex) {
                    // Completed steps
                    $element.removeClass('active incomplete').addClass(stepStatus[index] ? 'completed' : 'incomplete');
                    $element.html(stepStatus[index] ? 
                        '<svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' : 
                        (index + 1).toString());
                } else if (index === stepIndex) {
                    // Current step
                    $element.addClass('active').removeClass('completed incomplete');
                    $element.html((index + 1).toString());
                } else {
                    // Future steps
                    $element.removeClass('active completed incomplete');
                    $element.html((index + 1).toString());
                }
            });
            
            // Update progress lines
            $.each(progressLines, function(index, line) {
                const $element = $('#' + line);
                if (index < stepIndex - 1) {
                    $element.addClass(stepStatus[index] ? 'completed' : 'incomplete').removeClass('active');
                } else if (index === stepIndex - 1) {
                    $element.addClass('active').removeClass('completed incomplete');
                } else {
                    $element.removeClass('active completed incomplete');
                }
            });
            
            currentStep = stepIndex;
        }
        
        // Check if step is complete
        function checkStepCompletion(stepIndex) {
            switch(stepIndex) {
                case 0: // Step 1
                    const nameEn = $('#nameEn').val();
                    const nameTh = $('#nameTh').val();
                    const idCard = $('#idCard').val();
                    const school = $('#school').val();
                    const email = $('#email').val();
                    const phone = $('#phone').val();
                    const hskLevel = $('input[name="hskLevel"]:checked').val();
                    
                    stepStatus[0] = nameEn && nameTh && idCard && school && email && phone && hskLevel;
                    break;
                case 1: // Step 2
                    const examMonth = $('input[name="examMonth"]:checked').val();
                    const examDate = $('input[name="examDate"]:checked').val();
                    
                    stepStatus[1] = examMonth && examDate;
                    break;
                case 2: // Step 3
                    const region = $('input[name="region"]:checked').val();
                    const testCenter = $('input[name="testCenter"]:checked').val();
                    
                    stepStatus[2] = region && testCenter;
                    break;
                case 3: // Step 4
                    const terms = $('#terms').prop('checked');
                    
                    stepStatus[3] = terms;
                    break;
            }
        }
        
        // Step 1 to Step 2
        $('#step1Next').on('click', function() {
            // Check completion status
            checkStepCompletion(0);
            showStep(1);
        });
        
        // Step 1 Skip
        $('#step1Skip').on('click', function() {
            showStep(1);
        });
        
        // Step 2 to Step 1
        $('#step2Prev').on('click', function() {
            showStep(0);
        });
        
        // Step 2 to Step 3
        $('#step2Next').on('click', function() {
            // Check completion status
            checkStepCompletion(1);
            showStep(2);
        });
        
        // Step 2 Skip
        $('#step2Skip').on('click', function() {
            showStep(2);
        });
        
        // Step 3 to Step 2
        $('#step3Prev').on('click', function() {
            showStep(1);
        });
        
        // Step 3 to Step 4
        $('#step3Next').on('click', function() {
            // Check completion status
            checkStepCompletion(2);
            
            // Update confirmation page
            updateConfirmationPage();
            
            showStep(3);
        });
        
        // Step 3 Skip
        $('#step3Skip').on('click', function() {
            updateConfirmationPage();
            showStep(3);
        });
        
        // Step 4 to Step 3
        $('#step4Prev').on('click', function() {
            showStep(2);
        });
        
        // Make step indicators clickable
        $('.progress-step').on('click', function() {
            const stepIndex = parseInt($(this).data('step'));
            showStep(stepIndex);
        });
        
        // Update confirmation page with form data
        function updateConfirmationPage() {
            $('#confirmNameEn').text($('#nameEn').val() || '-');
            $('#confirmNameTh').text($('#nameTh').val() || '-');
            $('#confirmIdCard').text($('#idCard').val() || '-');
            $('#confirmSchool').text($('#school').val() || '-');
            $('#confirmEmail').text($('#email').val() || '-');
            $('#confirmPhone').text($('#phone').val() || '-');
            
            const hskLevel = $('input[name="hskLevel"]:checked').val();
            $('#confirmHskLevel').text(hskLevel ? `HSK ${hskLevel}` : '-');
            
            const examDate = $('input[name="examDate"]:checked').val();
            if (examDate) {
                const date = new Date(examDate);
                const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
                $('#confirmExamDate').text(date.toLocaleDateString('th-TH', options) + ' เวลา 09:00 - 12:00 น.');
            } else {
                $('#confirmExamDate').text('-');
            }
            
            const testCenter = $('input[name="testCenter"]:checked');
            if (testCenter.length) {
                const centerLabel = testCenter.next().find('h5').text();
                const centerAddress = testCenter.next().find('p').first().text();
                $('#confirmTestCenter').text(`${centerLabel} (${centerAddress})`);
            } else {
                $('#confirmTestCenter').text('-');
            }
        }
        
        // Handle exam month selection
        $('input[name="examMonth"]').on('change', function() {
            // Hide all date selections
            $('#decemberDates, #februaryDates, #aprilDates, #juneDates').addClass('d-none');
            
            // Show selected month dates
            if (this.value === 'december') {
                $('#decemberDates').removeClass('d-none');
            } else if (this.value === 'february') {
                $('#februaryDates').removeClass('d-none');
            } else if (this.value === 'april') {
                $('#aprilDates').removeClass('d-none');
            } else if (this.value === 'june') {
                $('#juneDates').removeClass('d-none');
            }
            
            // Clear any previously selected date
            $('input[name="examDate"]').prop('checked', false);
        });
        
        // Handle region selection
        $('input[name="region"]').on('change', function() {
            // Hide all center selections
            $('#centralCenters, #northCenters, #northeastCenters, #eastCenters, #westCenters, #southCenters').addClass('d-none');
            
            // Show selected region centers
            if (this.value === 'central') {
                $('#centralCenters').removeClass('d-none');
            } else if (this.value === 'north') {
                $('#northCenters').removeClass('d-none');
            } else if (this.value === 'northeast') {
                $('#northeastCenters').removeClass('d-none');
            } else if (this.value === 'east') {
                $('#eastCenters').removeClass('d-none');
            } else if (this.value === 'west') {
                $('#westCenters').removeClass('d-none');
            } else if (this.value === 'south') {
                $('#southCenters').removeClass('d-none');
            }
            
            // Clear any previously selected center
            $('input[name="testCenter"]').prop('checked', false);
        });
        
        // Initialize Bootstrap modal
        const successModal = new bootstrap.Modal(document.getElementById('successModal'));
        
        // Form submission
        $('#hskRegistrationForm').on('submit', function(e) {
            e.preventDefault();
            
            // Update modal content
            $('#modalName').text($('#nameEn').val() || '-');
            
            const hskLevel = $('input[name="hskLevel"]:checked').val();
            $('#modalLevel').text(hskLevel ? `HSK ${hskLevel}` : '-');
            
            const examDate = $('input[name="examDate"]:checked').val();
            if (examDate) {
                const date = new Date(examDate);
                const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
                $('#modalDate').text(date.toLocaleDateString('th-TH', options) + ' เวลา 09:00 - 12:00 น.');
            } else {
                $('#modalDate').text('-');
            }
            
            const testCenter = $('input[name="testCenter"]:checked');
            if (testCenter.length) {
                const centerLabel = testCenter.next().find('h5').text();
                $('#modalCenter').text(centerLabel);
            } else {
                $('#modalCenter').text('-');
            }
            
            // Show success modal
            successModal.show();
        });
        
        // Close modal
        $('#closeModal').on('click', function() {
            successModal.hide();
            // Reset form and go back to step 1
            $('#hskRegistrationForm')[0].reset();
            
            // Reset step status
            for (let i = 0; i < 4; i++) {
                stepStatus[i] = false;
            }
            
            showStep(0);
        });
        
        // Initialize the form
        showStep(0);
    });
</script>
@endpush

@push('css-stack')
    <link rel="stylesheet" type="text/css" href="{{ _asset('modules/core/vendor/select2/4.1.0-rc.0/select2.min.css') }}">

    <style type="text/css">
        .select2-results__options{
            grid-template-columns: repeat(2, minmax(0, 1fr));
            display: grid
        }
 
        .chinese-pattern {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 14rem;
            position: relative;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e53e3e' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        .chinese-pattern > .inset-0{
            opacity: 0.9; 
            /* --tw-gradient-to: #ef4444 var(--tw-gradient-to-position); 
            --tw-gradient-from: #dc2626 var(--tw-gradient-from-position); */
            --tw-gradient-to: #851B1D var(--tw-gradient-to-position); 
            --tw-gradient-from: #851B1D var(--tw-gradient-from-position);
            /* --tw-gradient-to: #5D1519 var(--tw-gradient-to-position); 
            --tw-gradient-from: #5D1519 var(--tw-gradient-from-position); */
            /* --tw-gradient-to: rgb(220 38 38 / 0) var(--tw-gradient-to-position); */
            --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to); 
            background-image: linear-gradient(to right, var(--tw-gradient-stops)); 
            /* display: flex;
            justify-content: center; 
            align-items: center;  */
            inset: 0px;
        } 
        .chinese-pattern > .inset-0 p {
            --tw-text-opacity: 1;
            color: rgb(254 249 195 / var(--tw-text-opacity, 1));
        }
        .chinese-pattern > .inset-0 .chinese-symbol{
            font-family: 'SimSun', 'STSong', serif;
            --tw-text-opacity: 1;
            color: rgb(253 224 71 / var(--tw-text-opacity, 1));
            font-weight: 700;
            font-size: 3.75rem;
            line-height: 1;
        } 
        .chinese-pattern > .chinese-symbol {
            font-family: 'SimSun', 'STSong', serif; 
            opacity: 0.2;
            --tw-text-opacity: 1;
            color: rgb(255 255 255 / var(--tw-text-opacity, 1));
            font-size: 3.75rem;
            line-height: 1;
            position: absolute;
        } 
        .chinese-pattern > .chinese-symbol.top-4 {
            top: 1rem;
            left: 1rem;
        } 
        .chinese-pattern > .chinese-symbol.bottom-4 {
            right: 1rem;
            bottom: 1rem;
        } 
 
        .shine-effect {
            position: relative;
            overflow: hidden;
        }

        .shine-effect::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
            transform: rotate(30deg);
            animation: shine 3s infinite;
        }
        @keyframes shine {
            0% {transform: translateX(-100%) rotate(30deg);}
            100% {transform: translateX(100%) rotate(30deg);}
        }
        .floating {animation: floating 3s ease-in-out infinite;}
        @keyframes floating {
            0% {transform: translateY(0px);}
            50% {transform: translateY(-10px);}
            100% {transform: translateY(0px);}
        }


        .transition-transform {
            transition-property: transform;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }


        /* ========================= */

        body {
            font-family: 'Prompt', sans-serif;
            background-color: #f8f8f8;
        }
        
        /* .chinese-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e53e3e' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        } */
        .bg-gradient-red {
            background: linear-gradient(135deg, #dc3545 0%, #b02a37 100%);
        }
        .chinese-symbol {
            font-family: 'SimSun', 'STSong', serif;
        }
        .form-control:focus, .form-check-input:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
        }
        .step-transition {
            transition: all 0.3s ease-in-out;
        }
        .date-radio:checked + label {
            border-color: #dc3545;
            background-color: #fff5f5;
            box-shadow: 0 0 0 2px #dc3545;
        }
        .center-radio:checked + label {
            border-color: #dc3545;
            box-shadow: 0 0 0 2px #dc3545;
        }
        .progress-step {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #dee2e6;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .progress-step.active {
            background-color: #dc3545;
            color: white;
        }
        .progress-step.completed {
            background-color: #198754;
            color: white;
        }
        .progress-step.incomplete {
            background-color: #ffc107;
            color: #212529;
        }
        .progress-line {
            height: 2px;
            background-color: #dee2e6;
            flex-grow: 1;
            transition: all 0.3s ease;
        }
        .progress-line.active {
            background-color: #dc3545;
        }
        .progress-line.completed {
            background-color: #198754;
        }
        .progress-line.incomplete {
            background-color: #ffc107;
        }
        .input-animated {
            transition: all 0.3s ease;
        }
        .input-animated:focus-within {
            transform: translateY(-5px);
        }
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
            0% {transform: translateY(0px);}
            50% {transform: translateY(-10px);}
            100% {transform: translateY(0px);}
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from {opacity: 0; transform: translateY(20px);}
            to {opacity: 1; transform: translateY(0);}
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% {box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);}
            70% {box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);}
            100% {box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);}
        }
        .custom-radio-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .custom-radio-card:hover {
            background-color: #fff1f1;
        }
        input[type="radio"]:checked + .custom-radio-card {
            border-color: #dc3545;
            background-color: #fff1f1;
            box-shadow: 0 0 0 1px #dc3545;
        }
        .center-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            overflow: hidden;
            transition: all 0.2s ease;
            height: 100%;
        }
        .center-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        input[type="radio"]:checked + .center-card {
            border-color: #dc3545;
            box-shadow: 0 0 0 1px #dc3545, 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .center-image {
            height: 160px;
            background-color: #e9ecef;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .step-content {
            display: none;
        }
        .step-content.active {
            display: block;
        }
        .btn-skip {
            color: #6c757d;
            background-color: transparent;
            border: none;
            text-decoration: underline;
            transition: all 0.2s ease;
        }
        .btn-skip:hover {
            color: #dc3545;
        }

    </style>
@endpush
