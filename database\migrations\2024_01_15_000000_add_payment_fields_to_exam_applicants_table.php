<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPaymentFieldsToExamApplicantsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('exam__applicants', function (Blueprint $table) {
            $table->string('payment_status')->default('pending')->after('checkname_at')->comment('pending, paid, failed, cancelled');
            $table->string('payment_method')->nullable()->after('payment_status')->comment('qr_code, bank_transfer, cash');
            $table->string('payment_reference')->nullable()->after('payment_method')->comment('Transaction reference from payment gateway');
            $table->decimal('payment_amount', 10, 2)->nullable()->after('payment_reference')->comment('Payment amount');
            $table->timestamp('payment_date')->nullable()->after('payment_amount')->comment('Date when payment was confirmed');
            $table->json('payment_details')->nullable()->after('payment_date')->comment('Additional payment details from gateway');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('exam__applicants', function (Blueprint $table) {
            $table->dropColumn([
                'payment_status',
                'payment_method', 
                'payment_reference',
                'payment_amount',
                'payment_date',
                'payment_details'
            ]);
        });
    }
}
