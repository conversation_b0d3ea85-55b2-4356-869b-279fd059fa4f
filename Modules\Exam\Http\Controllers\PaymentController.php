<?php

namespace Modules\Exam\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Core\Http\Controllers\BasePublicController;
use Modules\Core\Services\Payment\QRPaymentService;
use Modules\Exam\Entities\Applicant;
use Modules\Exam\Entities\Exam;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

class PaymentController extends BasePublicController
{
    protected $qrPaymentService;

    public function __construct(QRPaymentService $qrPaymentService)
    {
        $this->qrPaymentService = $qrPaymentService;
    }

    /**
     * Generate QR code for exam payment
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateQR(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'exam_id' => 'required|exists:exam__exams,id',
                'applicant_id' => 'required|exists:exam__applicants,id',
            ]);

            $exam = Exam::findOrFail($request->exam_id);
            $applicant = Applicant::findOrFail($request->applicant_id);

            // Check if user owns this applicant record
            if ($applicant->user_id !== auth()->id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Check if already paid
            if ($applicant->payment_status === 'paid') {
                return response()->json(['error' => 'Payment already completed'], 400);
            }

            // Get exam fee
            $examType = $exam->type;
            $amount = config("qr_payment.exam.fees_by_type.{$examType}", config('qr_payment.exam.default_fee'));

            // Generate payment reference
            $reference = 'EXAM' . $exam->id . 'APP' . $applicant->id . date('YmdHis');

            $paymentData = [
                'amount' => $amount,
                'reference' => $reference,
                'exam_id' => $exam->id,
                'applicant_id' => $applicant->id,
                'user_id' => auth()->id(),
            ];

            $qrData = $this->qrPaymentService->generateQRCode($paymentData);

            // Update applicant with payment reference
            $applicant->update([
                'payment_reference' => $reference,
                'payment_amount' => $amount,
                'payment_method' => 'qr_code',
                'payment_details' => json_encode($qrData),
            ]);

            return response()->json([
                'success' => true,
                'data' => $qrData,
                'exam' => [
                    'id' => $exam->id,
                    'title' => $exam->translate()->title,
                    'type' => $exam->type,
                ],
                'applicant' => [
                    'id' => $applicant->id,
                    'name' => $applicant->fullname,
                ],
            ]);

        } catch (Exception $e) {
            Log::error('QR Generation Error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to generate QR code'], 500);
        }
    }

    /**
     * Check payment status
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkStatus(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'applicant_id' => 'required|exists:exam__applicants,id',
            ]);

            $applicant = Applicant::findOrFail($request->applicant_id);

            // Check if user owns this applicant record
            if ($applicant->user_id !== auth()->id()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Check payment status from gateway if reference exists
            if ($applicant->payment_reference) {
                $gatewayStatus = $this->qrPaymentService->verifyPayment($applicant->payment_reference);
                
                // Update status if changed
                if ($gatewayStatus['status'] === 'paid' && $applicant->payment_status !== 'paid') {
                    $applicant->update([
                        'payment_status' => 'paid',
                        'payment_date' => now(),
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'payment_status' => $applicant->payment_status,
                'payment_date' => $applicant->payment_date,
                'payment_amount' => $applicant->payment_amount,
                'payment_reference' => $applicant->payment_reference,
            ]);

        } catch (Exception $e) {
            Log::error('Payment Status Check Error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to check payment status'], 500);
        }
    }

    /**
     * Manual payment confirmation (for admin or testing)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function confirmPayment(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'applicant_id' => 'required|exists:exam__applicants,id',
                'payment_reference' => 'required|string',
            ]);

            $applicant = Applicant::findOrFail($request->applicant_id);

            // For testing purposes, allow manual confirmation
            // In production, this should be restricted to admin users
            if (!auth()->user()->hasRole(['Admin', 'Manager'])) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $applicant->update([
                'payment_status' => 'paid',
                'payment_date' => now(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment confirmed successfully',
                'payment_status' => $applicant->payment_status,
                'payment_date' => $applicant->payment_date,
            ]);

        } catch (Exception $e) {
            Log::error('Payment Confirmation Error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to confirm payment'], 500);
        }
    }

    /**
     * Payment callback from gateway
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function callback(Request $request): JsonResponse
    {
        try {
            // This would handle callbacks from payment gateways
            // Implementation depends on the specific gateway being used
            
            Log::info('Payment Callback Received', $request->all());

            $reference = $request->input('reference');
            $status = $request->input('status');
            $amount = $request->input('amount');

            if ($reference && $status === 'success') {
                $applicant = Applicant::where('payment_reference', $reference)->first();
                
                if ($applicant) {
                    $applicant->update([
                        'payment_status' => 'paid',
                        'payment_date' => now(),
                        'payment_details' => json_encode($request->all()),
                    ]);
                }
            }

            return response()->json(['success' => true]);

        } catch (Exception $e) {
            Log::error('Payment Callback Error: ' . $e->getMessage());
            return response()->json(['error' => 'Callback processing failed'], 500);
        }
    }

    /**
     * Generate QR code image
     *
     * @param string $data
     * @return \Illuminate\Http\Response
     */
    public function qrImage($data)
    {
        try {
            $qrString = base64_decode($data);
            
            // Generate QR code image using a QR library
            // For now, return a placeholder response
            $qrCodeUrl = "https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=" . urlencode($qrString);
            
            return redirect($qrCodeUrl);

        } catch (Exception $e) {
            Log::error('QR Image Generation Error: ' . $e->getMessage());
            abort(404);
        }
    }
}
