<?php

return [
    /*
    |--------------------------------------------------------------------------
    | QR Payment Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for QR payment systems including PromptPay and other
    | Thai QR payment gateways
    |
    */

    'default_gateway' => env('QR_PAYMENT_DEFAULT_GATEWAY', 'promptpay'),

    'gateways' => [
        'promptpay' => [
            'name' => 'PromptPay',
            'enabled' => env('QR_PAYMENT_PROMPTPAY_ENABLED', true),
            'phone_number' => env('QR_PAYMENT_PROMPTPAY_PHONE', '**********'),
            'citizen_id' => env('QR_PAYMENT_PROMPTPAY_CITIZEN_ID', null),
            'tax_id' => env('QR_PAYMENT_PROMPTPAY_TAX_ID', null),
            'e_wallet_id' => env('QR_PAYMENT_PROMPTPAY_EWALLET_ID', null),
            'bank_account' => env('QR_PAYMENT_PROMPTPAY_BANK_ACCOUNT', null),
        ],

        'thai_qr' => [
            'name' => 'Thai QR Payment',
            'enabled' => env('QR_PAYMENT_THAI_QR_ENABLED', false),
            'merchant_id' => env('QR_PAYMENT_THAI_QR_MERCHANT_ID', ''),
            'terminal_id' => env('QR_PAYMENT_THAI_QR_TERMINAL_ID', ''),
            'api_key' => env('QR_PAYMENT_THAI_QR_API_KEY', ''),
            'api_secret' => env('QR_PAYMENT_THAI_QR_API_SECRET', ''),
            'sandbox' => env('QR_PAYMENT_THAI_QR_SANDBOX', true),
            'api_url' => env('QR_PAYMENT_THAI_QR_API_URL', 'https://api-sandbox.partners.scb/partners/sandbox'),
        ],

        'kbank' => [
            'name' => 'K PLUS',
            'enabled' => env('QR_PAYMENT_KBANK_ENABLED', false),
            'merchant_id' => env('QR_PAYMENT_KBANK_MERCHANT_ID', ''),
            'api_key' => env('QR_PAYMENT_KBANK_API_KEY', ''),
            'api_secret' => env('QR_PAYMENT_KBANK_API_SECRET', ''),
            'sandbox' => env('QR_PAYMENT_KBANK_SANDBOX', true),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Payment Settings
    |--------------------------------------------------------------------------
    */
    'currency' => 'THB',
    'default_amount' => 500.00,
    'timeout_minutes' => 15, // QR code expiration time
    'callback_url' => env('QR_PAYMENT_CALLBACK_URL', '/api/payment/callback'),
    'success_url' => env('QR_PAYMENT_SUCCESS_URL', '/payment/success'),
    'cancel_url' => env('QR_PAYMENT_CANCEL_URL', '/payment/cancel'),

    /*
    |--------------------------------------------------------------------------
    | QR Code Settings
    |--------------------------------------------------------------------------
    */
    'qr_code' => [
        'size' => 300,
        'margin' => 10,
        'format' => 'png',
        'error_correction' => 'M', // L, M, Q, H
    ],

    /*
    |--------------------------------------------------------------------------
    | Exam Payment Settings
    |--------------------------------------------------------------------------
    */
    'exam' => [
        'default_fee' => 500.00,
        'fees_by_type' => [
            'hsk' => 500.00,
            'hskk' => 600.00,
            'yct' => 400.00,
        ],
        'auto_approve' => env('QR_PAYMENT_AUTO_APPROVE', false),
        'require_slip_upload' => env('QR_PAYMENT_REQUIRE_SLIP', true),
    ],
];
